from typing import Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_
from uuid import UUID

from app.models.user import User
from app.schemas.user import UserCreate, UserUpdate


def get_user_by_id(db: Session, user_id: UUID) -> Optional[User]:
    """Get user by ID"""
    return db.query(User).filter(User.user_id == user_id).first()


def get_user_by_email(db: Session, email: str) -> Optional[User]:
    """Get user by email"""
    return db.query(User).filter(User.email == email).first()


def get_user_by_provider(
    db: Session, email: str, auth_provider: str, provider_user_id: str = None
) -> Optional[User]:
    """Get user by email and auth provider"""
    query = db.query(User).filter(
        and_(
            User.email == email,
            User.auth_provider == auth_provider
        )
    )
    
    if provider_user_id:
        query = query.filter(User.provider_user_id == provider_user_id)
    
    return query.first()


def create_user(db: Session, user: UserCreate) -> User:
    """Create new user"""
    db_user = User(
        email=user.email,
        display_name=user.display_name,
        photo_url=user.photo_url,
        auth_provider=user.auth_provider,
        provider_user_id=user.provider_user_id,
        password_hash=None  # OAuth users don't have passwords
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user


def update_user(db: Session, user_id: UUID, user_update: UserUpdate) -> Optional[User]:
    """Update user"""
    db_user = get_user_by_id(db, user_id)
    if not db_user:
        return None
    
    update_data = user_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_user, field, value)
    
    db.commit()
    db.refresh(db_user)
    return db_user


def update_last_login(db: Session, user_id: UUID) -> Optional[User]:
    """Update user's last login timestamp"""
    from datetime import datetime
    
    db_user = get_user_by_id(db, user_id)
    if not db_user:
        return None
    
    db_user.last_login_at = datetime.utcnow()
    db.commit()
    db.refresh(db_user)
    return db_user
