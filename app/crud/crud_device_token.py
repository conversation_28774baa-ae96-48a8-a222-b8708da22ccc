"""
CRUD operations for device tokens.
"""

from typing import List, Optional
from uuid import UUID
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.crud.base import CRUDBase
from app.models.device_token import DeviceToken
from app.schemas.device_token import DeviceTokenCreate, DeviceTokenUpdate


class CRUDDeviceToken(CRUDBase[DeviceToken, DeviceTokenCreate, DeviceTokenUpdate]):
    """CRUD operations for device tokens"""
    
    def get_by_token(self, db: Session, *, device_token: str) -> Optional[DeviceToken]:
        """Get device token by token string"""
        return db.query(DeviceToken).filter(
            DeviceToken.device_token == device_token
        ).first()
    
    def get_by_user(self, db: Session, *, user_id: UUID) -> List[DeviceToken]:
        """Get all device tokens for a user"""
        return db.query(DeviceToken).filter(
            DeviceToken.user_id == user_id
        ).all()
    
    def get_active_by_user(self, db: Session, *, user_id: UUID) -> List[DeviceToken]:
        """Get active device tokens for a user"""
        return db.query(DeviceToken).filter(
            and_(
                DeviceToken.user_id == user_id,
                DeviceToken.is_active == True
            )
        ).all()
    
    def get_by_device_id(
        self, 
        db: Session, 
        *, 
        user_id: UUID, 
        device_id: str
    ) -> Optional[DeviceToken]:
        """Get device token by user and device ID"""
        return db.query(DeviceToken).filter(
            and_(
                DeviceToken.user_id == user_id,
                DeviceToken.device_id == device_id
            )
        ).first()
    
    def register_or_update(
        self,
        db: Session,
        *,
        user_id: UUID,
        device_token: str,
        device_type: str,
        device_id: Optional[str] = None,
        app_version: Optional[str] = None,
        os_version: Optional[str] = None
    ) -> DeviceToken:
        """
        Register new device token or update existing one
        
        This method handles the logic of:
        1. Check if token already exists
        2. Check if device_id already exists for this user
        3. Create new or update existing token
        4. Deactivate old tokens if needed
        """
        
        # Check if this exact token already exists
        existing_token = self.get_by_token(db, device_token=device_token)
        
        if existing_token:
            # Token exists, update it
            if existing_token.user_id != user_id:
                # Token belongs to different user, deactivate old and create new
                existing_token.is_active = False
                db.add(existing_token)
                
                # Create new token for current user
                new_token = DeviceToken(
                    user_id=user_id,
                    device_token=device_token,
                    device_type=device_type,
                    device_id=device_id,
                    app_version=app_version,
                    os_version=os_version,
                    is_active=True,
                    last_used_at=datetime.utcnow()
                )
                db.add(new_token)
                db.commit()
                db.refresh(new_token)
                return new_token
            else:
                # Same user, update existing token
                existing_token.device_type = device_type
                existing_token.device_id = device_id
                existing_token.app_version = app_version
                existing_token.os_version = os_version
                existing_token.is_active = True
                existing_token.last_used_at = datetime.utcnow()
                existing_token.updated_at = datetime.utcnow()
                
                db.add(existing_token)
                db.commit()
                db.refresh(existing_token)
                return existing_token
        
        # If device_id is provided, check for existing device
        if device_id:
            existing_device = self.get_by_device_id(
                db, user_id=user_id, device_id=device_id
            )
            if existing_device:
                # Update existing device with new token
                existing_device.device_token = device_token
                existing_device.device_type = device_type
                existing_device.app_version = app_version
                existing_device.os_version = os_version
                existing_device.is_active = True
                existing_device.last_used_at = datetime.utcnow()
                existing_device.updated_at = datetime.utcnow()
                
                db.add(existing_device)
                db.commit()
                db.refresh(existing_device)
                return existing_device
        
        # Create completely new token
        new_token = DeviceToken(
            user_id=user_id,
            device_token=device_token,
            device_type=device_type,
            device_id=device_id,
            app_version=app_version,
            os_version=os_version,
            is_active=True,
            last_used_at=datetime.utcnow()
        )
        
        db.add(new_token)
        db.commit()
        db.refresh(new_token)
        return new_token
    
    def deactivate_token(self, db: Session, *, token_id: UUID) -> bool:
        """Deactivate a device token"""
        token = self.get(db, id=token_id)
        if token:
            token.is_active = False
            token.updated_at = datetime.utcnow()
            db.add(token)
            db.commit()
            return True
        return False
    
    def deactivate_user_tokens(self, db: Session, *, user_id: UUID) -> int:
        """Deactivate all tokens for a user"""
        count = db.query(DeviceToken).filter(
            and_(
                DeviceToken.user_id == user_id,
                DeviceToken.is_active == True
            )
        ).update({
            "is_active": False,
            "updated_at": datetime.utcnow()
        })
        db.commit()
        return count
    
    def cleanup_inactive_tokens(self, db: Session, *, days_old: int = 30) -> int:
        """Remove inactive tokens older than specified days"""
        cutoff_date = datetime.utcnow() - timedelta(days=days_old)
        
        count = db.query(DeviceToken).filter(
            and_(
                DeviceToken.is_active == False,
                DeviceToken.updated_at < cutoff_date
            )
        ).delete()
        
        db.commit()
        return count
    
    def get_tokens_for_batch(
        self,
        db: Session,
        *,
        user_ids: List[UUID],
        device_types: Optional[List[str]] = None
    ) -> List[DeviceToken]:
        """Get active tokens for batch notification"""
        query = db.query(DeviceToken).filter(
            and_(
                DeviceToken.user_id.in_(user_ids),
                DeviceToken.is_active == True
            )
        )
        
        if device_types:
            query = query.filter(DeviceToken.device_type.in_(device_types))
        
        return query.all()
    
    def update_last_used(self, db: Session, *, token_id: UUID) -> bool:
        """Update last used timestamp"""
        token = self.get(db, id=token_id)
        if token:
            token.last_used_at = datetime.utcnow()
            db.add(token)
            db.commit()
            return True
        return False


device_token = CRUDDeviceToken(DeviceToken)
