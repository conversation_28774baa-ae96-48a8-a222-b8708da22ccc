"""
CRUD operations for notification templates.
"""

from typing import List, Optional
from uuid import UUID
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.crud.base import CRUDBase
from app.models.notification_template import NotificationTemplate
from app.schemas.notification_template import NotificationTemplateCreate, NotificationTemplateUpdate


class CRUDNotificationTemplate(CRUDBase[NotificationTemplate, NotificationTemplateCreate, NotificationTemplateUpdate]):
    """CRUD operations for notification templates"""
    
    def get_by_key(self, db: Session, *, template_key: str) -> Optional[NotificationTemplate]:
        """Get template by key"""
        return db.query(NotificationTemplate).filter(
            NotificationTemplate.template_key == template_key
        ).first()
    
    def get_active_by_key(self, db: Session, *, template_key: str) -> Optional[NotificationTemplate]:
        """Get active template by key"""
        return db.query(NotificationTemplate).filter(
            and_(
                NotificationTemplate.template_key == template_key,
                NotificationTemplate.is_active == True
            )
        ).first()
    
    def get_by_category(self, db: Session, *, category: str) -> List[NotificationTemplate]:
        """Get templates by category"""
        return db.query(NotificationTemplate).filter(
            NotificationTemplate.category == category
        ).all()
    
    def get_active_by_category(self, db: Session, *, category: str) -> List[NotificationTemplate]:
        """Get active templates by category"""
        return db.query(NotificationTemplate).filter(
            and_(
                NotificationTemplate.category == category,
                NotificationTemplate.is_active == True
            )
        ).all()
    
    def get_by_type(self, db: Session, *, notification_type: str) -> List[NotificationTemplate]:
        """Get templates by notification type"""
        return db.query(NotificationTemplate).filter(
            NotificationTemplate.notification_type == notification_type
        ).all()
    
    def get_active_templates(self, db: Session) -> List[NotificationTemplate]:
        """Get all active templates"""
        return db.query(NotificationTemplate).filter(
            NotificationTemplate.is_active == True
        ).all()
    
    def search_templates(
        self,
        db: Session,
        *,
        search_term: Optional[str] = None,
        category: Optional[str] = None,
        notification_type: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> List[NotificationTemplate]:
        """Search templates with filters"""
        query = db.query(NotificationTemplate)
        
        if search_term:
            search_filter = or_(
                NotificationTemplate.template_key.ilike(f"%{search_term}%"),
                NotificationTemplate.template_name.ilike(f"%{search_term}%"),
                NotificationTemplate.description.ilike(f"%{search_term}%")
            )
            query = query.filter(search_filter)
        
        if category:
            query = query.filter(NotificationTemplate.category == category)
        
        if notification_type:
            query = query.filter(NotificationTemplate.notification_type == notification_type)
        
        if is_active is not None:
            query = query.filter(NotificationTemplate.is_active == is_active)
        
        return query.all()
    
    def create_template(
        self,
        db: Session,
        *,
        obj_in: NotificationTemplateCreate
    ) -> NotificationTemplate:
        """Create new template with validation"""
        # Check if template key already exists
        existing = self.get_by_key(db, template_key=obj_in.template_key)
        if existing:
            raise ValueError(f"Template with key '{obj_in.template_key}' already exists")
        
        # Validate required languages
        required_languages = ["vi", "en"]
        for lang in required_languages:
            if lang not in obj_in.title_templates:
                raise ValueError(f"Missing title template for language: {lang}")
            if lang not in obj_in.body_templates:
                raise ValueError(f"Missing body template for language: {lang}")
        
        # Validate category
        valid_categories = ["marketing", "transactional", "system"]
        if obj_in.category not in valid_categories:
            raise ValueError(f"Invalid category. Must be one of: {valid_categories}")
        
        # Validate priority
        valid_priorities = ["high", "normal", "low"]
        if obj_in.priority not in valid_priorities:
            raise ValueError(f"Invalid priority. Must be one of: {valid_priorities}")
        
        return self.create(db, obj_in=obj_in)
    
    def update_template(
        self,
        db: Session,
        *,
        template_id: UUID,
        obj_in: NotificationTemplateUpdate
    ) -> Optional[NotificationTemplate]:
        """Update template with validation"""
        template = self.get(db, id=template_id)
        if not template:
            return None
        
        # Validate category if provided
        if obj_in.category:
            valid_categories = ["marketing", "transactional", "system"]
            if obj_in.category not in valid_categories:
                raise ValueError(f"Invalid category. Must be one of: {valid_categories}")
        
        # Validate priority if provided
        if obj_in.priority:
            valid_priorities = ["high", "normal", "low"]
            if obj_in.priority not in valid_priorities:
                raise ValueError(f"Invalid priority. Must be one of: {valid_priorities}")
        
        # Validate language templates if provided
        if obj_in.title_templates:
            required_languages = ["vi", "en"]
            for lang in required_languages:
                if lang not in obj_in.title_templates:
                    raise ValueError(f"Missing title template for language: {lang}")
        
        if obj_in.body_templates:
            required_languages = ["vi", "en"]
            for lang in required_languages:
                if lang not in obj_in.body_templates:
                    raise ValueError(f"Missing body template for language: {lang}")
        
        return self.update(db, db_obj=template, obj_in=obj_in)
    
    def activate_template(self, db: Session, *, template_id: UUID) -> bool:
        """Activate a template"""
        template = self.get(db, id=template_id)
        if template:
            template.is_active = True
            template.updated_at = datetime.utcnow()
            db.add(template)
            db.commit()
            return True
        return False
    
    def deactivate_template(self, db: Session, *, template_id: UUID) -> bool:
        """Deactivate a template"""
        template = self.get(db, id=template_id)
        if template:
            template.is_active = False
            template.updated_at = datetime.utcnow()
            db.add(template)
            db.commit()
            return True
        return False
    
    def get_template_stats(self, db: Session) -> dict:
        """Get template statistics"""
        total = db.query(NotificationTemplate).count()
        active = db.query(NotificationTemplate).filter(
            NotificationTemplate.is_active == True
        ).count()
        
        # Count by category
        categories = db.query(
            NotificationTemplate.category,
            db.func.count(NotificationTemplate.template_id)
        ).group_by(NotificationTemplate.category).all()
        
        # Count by type
        types = db.query(
            NotificationTemplate.notification_type,
            db.func.count(NotificationTemplate.template_id)
        ).group_by(NotificationTemplate.notification_type).all()
        
        return {
            "total": total,
            "active": active,
            "inactive": total - active,
            "by_category": {cat: count for cat, count in categories},
            "by_type": {type_: count for type_, count in types}
        }
    
    def duplicate_template(
        self,
        db: Session,
        *,
        template_id: UUID,
        new_key: str,
        new_name: str
    ) -> Optional[NotificationTemplate]:
        """Duplicate an existing template"""
        original = self.get(db, id=template_id)
        if not original:
            return None
        
        # Check if new key already exists
        existing = self.get_by_key(db, template_key=new_key)
        if existing:
            raise ValueError(f"Template with key '{new_key}' already exists")
        
        # Create duplicate
        duplicate_data = NotificationTemplateCreate(
            template_key=new_key,
            template_name=new_name,
            description=f"Copy of {original.template_name}",
            category=original.category,
            notification_type=original.notification_type,
            title_templates=original.title_templates,
            body_templates=original.body_templates,
            image_url=original.image_url,
            action_url=original.action_url,
            action_button_text=original.action_button_text,
            priority=original.priority,
            sound=original.sound,
            badge_count=original.badge_count,
            is_active=False  # Start as inactive
        )
        
        return self.create(db, obj_in=duplicate_data)


notification_template = CRUDNotificationTemplate(NotificationTemplate)
