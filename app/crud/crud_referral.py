from typing import List, Optional
from uuid import UUID
from datetime import datetime, timedelta, timezone
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.crud.base import CRUDBase
from app.models.referral_voucher import ReferralVoucher
from app.models.referral_history import ReferralHistory
from app.models.user import User
from app.schemas.referral import (
    ReferralVoucherCreate,
    ReferralVoucherUpdate,
    ReferralHistoryCreate,
    ReferralHistoryUpdate
)


class CRUDReferralVoucher(CRUDBase[ReferralVoucher, ReferralVoucherCreate, ReferralVoucherUpdate]):
    def create(self, db: Session, *, obj_in: ReferralVoucherCreate) -> ReferralVoucher:
        """Create a new referral voucher"""
        # Set expiration date to 1 year from now
        expires_at = datetime.now(timezone.utc) + timedelta(days=365)

        db_obj = ReferralVoucher(
            owner_user_id=obj_in.owner_user_id,
            source_referral_id=obj_in.source_referral_id,
            amount_usd=obj_in.amount_usd,
            status=obj_in.status,
            expires_at=expires_at
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_by_owner(self, db: Session, owner_user_id: UUID) -> List[ReferralVoucher]:
        """Get all vouchers for a user"""
        return db.query(ReferralVoucher).filter(ReferralVoucher.owner_user_id == owner_user_id).all()

    def get_available_vouchers(self, db: Session, owner_user_id: UUID) -> List[ReferralVoucher]:
        """Get available vouchers for a user"""
        return db.query(ReferralVoucher).filter(
            and_(
                ReferralVoucher.owner_user_id == owner_user_id,
                ReferralVoucher.status == "available",
                or_(
                    ReferralVoucher.expires_at.is_(None),
                    ReferralVoucher.expires_at > datetime.now(timezone.utc)
                )
            )
        ).all()

    def get_first_available_voucher(self, db: Session, owner_user_id: UUID) -> Optional[ReferralVoucher]:
        """Get the first available voucher for a user"""
        return db.query(ReferralVoucher).filter(
            and_(
                ReferralVoucher.owner_user_id == owner_user_id,
                ReferralVoucher.status == "available",
                or_(
                    ReferralVoucher.expires_at.is_(None),
                    ReferralVoucher.expires_at > datetime.now(timezone.utc)
                )
            )
        ).order_by(ReferralVoucher.created_at).first()

    def update(self, db: Session, *, db_obj: ReferralVoucher, obj_in: ReferralVoucherUpdate) -> ReferralVoucher:
        """Update voucher"""
        update_data = obj_in.model_dump(exclude_unset=True)

        if obj_in.status == "used" and not db_obj.used_at:
            update_data["used_at"] = datetime.now(timezone.utc)
            
        for field, value in update_data.items():
            setattr(db_obj, field, value)
            
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def mark_as_used(self, db: Session, voucher_id: UUID) -> Optional[ReferralVoucher]:
        """Mark voucher as used"""
        voucher = self.get(db, voucher_id)
        if voucher and voucher.status == "available":
            return self.update(db, db_obj=voucher, obj_in=ReferralVoucherUpdate(status="used"))
        return None


class CRUDReferralHistory(CRUDBase[ReferralHistory, ReferralHistoryCreate, ReferralHistoryUpdate]):

    def get_by_referrer(self, db: Session, referrer_user_id: UUID) -> List[ReferralHistory]:
        """Get all referral history for a referrer"""
        return db.query(ReferralHistory).filter(ReferralHistory.referrer_user_id == referrer_user_id).all()

    def get_by_referred_user(self, db: Session, referred_user_id: UUID) -> Optional[ReferralHistory]:
        """Get referral history for a referred user"""
        return db.query(ReferralHistory).filter(ReferralHistory.referred_user_id == referred_user_id).first()

    def mark_as_completed(self, db: Session, history_id: UUID) -> Optional[ReferralHistory]:
        """Mark referral as completed"""
        history = self.get(db, id=history_id)
        if history and history.status == "pending":
            return self.update(db, db_obj=history, obj_in=ReferralHistoryUpdate(status="completed"))
        return None


class CRUDReferralUser:
    def get_by_referral_code(self, db: Session, referral_code: str) -> Optional[User]:
        """Get user by referral code"""
        return db.query(User).filter(User.referral_code == referral_code).first()

    def update_referral_info(self, db: Session, user_id: UUID, referred_by_user_id: UUID) -> Optional[User]:
        """Update user's referral information"""
        user = db.query(User).filter(User.user_id == user_id).first()
        if user:
            user.referred_by_user_id = referred_by_user_id
            db.add(user)
            db.commit()
            db.refresh(user)
        return user

    def mark_first_purchase(self, db: Session, user_id: UUID) -> Optional[User]:
        """Mark user as having made first purchase"""
        user = db.query(User).filter(User.user_id == user_id).first()
        if user:
            user.has_made_first_purchase = True
            db.add(user)
            db.commit()
            db.refresh(user)
        return user


# Create instances
referral_voucher = CRUDReferralVoucher(ReferralVoucher)
referral_history = CRUDReferralHistory(ReferralHistory)
referral_user = CRUDReferralUser()
