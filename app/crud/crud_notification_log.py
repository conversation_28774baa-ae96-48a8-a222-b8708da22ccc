"""
CRUD operations for notification logs and analytics.
"""

from typing import List, Dict, Any, Optional
from uuid import UUID
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, func, desc

from app.crud.base import CRUDBase
from app.models.notification_log import NotificationLog
from app.schemas.notification_log import NotificationLogCreate, NotificationLogUpdate


class CRUDNotificationLog(CRUDBase[NotificationLog, NotificationLogCreate, NotificationLogUpdate]):
    """CRUD operations for notification logs"""
    
    def get_by_user(
        self,
        db: Session,
        *,
        user_id: UUID,
        limit: int = 50,
        offset: int = 0
    ) -> List[NotificationLog]:
        """Get notification logs for a user"""
        return db.query(NotificationLog).filter(
            NotificationLog.user_id == user_id
        ).order_by(desc(NotificationLog.created_at)).offset(offset).limit(limit).all()
    
    def get_by_status(
        self,
        db: Session,
        *,
        status: str,
        limit: int = 100
    ) -> List[NotificationLog]:
        """Get notifications by delivery status"""
        return db.query(NotificationLog).filter(
            NotificationLog.delivery_status == status
        ).order_by(desc(NotificationLog.created_at)).limit(limit).all()
    
    def get_failed_notifications(
        self,
        db: Session,
        *,
        hours_ago: int = 24,
        limit: int = 100
    ) -> List[NotificationLog]:
        """Get failed notifications from last N hours"""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours_ago)
        
        return db.query(NotificationLog).filter(
            and_(
                NotificationLog.delivery_status == "failed",
                NotificationLog.created_at >= cutoff_time
            )
        ).order_by(desc(NotificationLog.created_at)).limit(limit).all()
    
    def get_delivery_stats(
        self,
        db: Session,
        *,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Get delivery statistics"""
        query = db.query(NotificationLog)
        
        if start_date:
            query = query.filter(NotificationLog.created_at >= start_date)
        if end_date:
            query = query.filter(NotificationLog.created_at <= end_date)
        
        # Total counts by status
        status_counts = query.with_entities(
            NotificationLog.delivery_status,
            func.count(NotificationLog.log_id)
        ).group_by(NotificationLog.delivery_status).all()
        
        # Total notifications
        total = query.count()
        
        # Success rate
        sent_count = query.filter(
            NotificationLog.delivery_status.in_(["sent", "delivered", "clicked"])
        ).count()
        
        success_rate = (sent_count / total * 100) if total > 0 else 0
        
        # Click rate
        clicked_count = query.filter(
            NotificationLog.delivery_status == "clicked"
        ).count()
        
        click_rate = (clicked_count / sent_count * 100) if sent_count > 0 else 0
        
        return {
            "total_notifications": total,
            "sent_count": sent_count,
            "success_rate": round(success_rate, 2),
            "click_rate": round(click_rate, 2),
            "status_breakdown": {status: count for status, count in status_counts}
        }
    
    def get_template_performance(
        self,
        db: Session,
        *,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[Dict[str, Any]]:
        """Get performance metrics by template"""
        query = db.query(NotificationLog)
        
        if start_date:
            query = query.filter(NotificationLog.created_at >= start_date)
        if end_date:
            query = query.filter(NotificationLog.created_at <= end_date)
        
        # Group by notification type
        template_stats = query.with_entities(
            NotificationLog.notification_type,
            func.count(NotificationLog.log_id).label("total"),
            func.sum(
                func.case(
                    (NotificationLog.delivery_status.in_(["sent", "delivered", "clicked"]), 1),
                    else_=0
                )
            ).label("sent"),
            func.sum(
                func.case(
                    (NotificationLog.delivery_status == "clicked", 1),
                    else_=0
                )
            ).label("clicked")
        ).group_by(NotificationLog.notification_type).all()
        
        results = []
        for stat in template_stats:
            total = stat.total or 0
            sent = stat.sent or 0
            clicked = stat.clicked or 0
            
            success_rate = (sent / total * 100) if total > 0 else 0
            click_rate = (clicked / sent * 100) if sent > 0 else 0
            
            results.append({
                "notification_type": stat.notification_type,
                "total": total,
                "sent": sent,
                "clicked": clicked,
                "success_rate": round(success_rate, 2),
                "click_rate": round(click_rate, 2)
            })
        
        return sorted(results, key=lambda x: x["total"], reverse=True)
    
    def get_daily_stats(
        self,
        db: Session,
        *,
        days: int = 30
    ) -> List[Dict[str, Any]]:
        """Get daily notification statistics"""
        start_date = datetime.utcnow() - timedelta(days=days)
        
        daily_stats = db.query(
            func.date(NotificationLog.created_at).label("date"),
            func.count(NotificationLog.log_id).label("total"),
            func.sum(
                func.case(
                    (NotificationLog.delivery_status.in_(["sent", "delivered", "clicked"]), 1),
                    else_=0
                )
            ).label("sent"),
            func.sum(
                func.case(
                    (NotificationLog.delivery_status == "clicked", 1),
                    else_=0
                )
            ).label("clicked")
        ).filter(
            NotificationLog.created_at >= start_date
        ).group_by(
            func.date(NotificationLog.created_at)
        ).order_by(
            func.date(NotificationLog.created_at)
        ).all()
        
        results = []
        for stat in daily_stats:
            total = stat.total or 0
            sent = stat.sent or 0
            clicked = stat.clicked or 0
            
            success_rate = (sent / total * 100) if total > 0 else 0
            click_rate = (clicked / sent * 100) if sent > 0 else 0
            
            results.append({
                "date": stat.date.isoformat(),
                "total": total,
                "sent": sent,
                "clicked": clicked,
                "success_rate": round(success_rate, 2),
                "click_rate": round(click_rate, 2)
            })
        
        return results
    
    def get_user_engagement(
        self,
        db: Session,
        *,
        user_id: UUID,
        days: int = 30
    ) -> Dict[str, Any]:
        """Get user engagement metrics"""
        start_date = datetime.utcnow() - timedelta(days=days)
        
        user_logs = db.query(NotificationLog).filter(
            and_(
                NotificationLog.user_id == user_id,
                NotificationLog.created_at >= start_date
            )
        )
        
        total = user_logs.count()
        clicked = user_logs.filter(
            NotificationLog.delivery_status == "clicked"
        ).count()
        
        # Get notification types received
        type_counts = user_logs.with_entities(
            NotificationLog.notification_type,
            func.count(NotificationLog.log_id)
        ).group_by(NotificationLog.notification_type).all()
        
        click_rate = (clicked / total * 100) if total > 0 else 0
        
        return {
            "user_id": str(user_id),
            "period_days": days,
            "total_notifications": total,
            "clicked_notifications": clicked,
            "click_rate": round(click_rate, 2),
            "notification_types": {type_: count for type_, count in type_counts}
        }
    
    def cleanup_old_logs(
        self,
        db: Session,
        *,
        days_old: int = 30
    ) -> int:
        """Remove old notification logs"""
        cutoff_date = datetime.utcnow() - timedelta(days=days_old)
        
        count = db.query(NotificationLog).filter(
            NotificationLog.created_at < cutoff_date
        ).delete()
        
        db.commit()
        return count
    
    def mark_as_clicked(
        self,
        db: Session,
        *,
        log_id: UUID
    ) -> bool:
        """Mark notification as clicked"""
        log = self.get(db, id=log_id)
        if log:
            log.delivery_status = "clicked"
            log.clicked_at = datetime.utcnow()
            db.add(log)
            db.commit()
            return True
        return False


notification_log = CRUDNotificationLog(NotificationLog)
