"""
CRUD operations for users.
"""

from typing import Optional
from uuid import UUID
from datetime import datetime, timezone
from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.crud.base import CRUDBase
from app.models.user import User
from app.schemas.user import UserCreate, UserUpdate


class CRUDUser(CRUDBase[User, UserCreate, UserUpdate]):
    """CRUD operations for users"""

    def get_by_email(self, db: Session, *, email: str) -> Optional[User]:
        """Get user by email"""
        return db.query(User).filter(User.email == email).first()

    def get_by_provider(
        self,
        db: Session,
        *,
        email: str,
        auth_provider: str,
        provider_user_id: Optional[str] = None
    ) -> Optional[User]:
        """Get user by email and auth provider"""
        query = db.query(User).filter(
            and_(
                User.email == email,
                User.auth_provider == auth_provider
            )
        )

        if provider_user_id:
            query = query.filter(User.provider_user_id == provider_user_id)

        return query.first()

    def update_last_login(self, db: Session, *, user_id: UUID) -> Optional[User]:
        """Update user's last login timestamp"""
        user = self.get(db, id=user_id)
        if user:
            user.last_login_at = datetime.now(timezone.utc)
            db.add(user)
            db.commit()
            db.refresh(user)
        return user


user = CRUDUser(User)