"""
Celery tasks for push notifications.
"""

import logging
import asyncio
from typing import List, Dict, Any, Optional
from uuid import UUI<PERSON>
from celery import current_task
from sqlalchemy.orm import Session

from app.core.celery_app import celery_app
from app.db.session import SessionLocal
from app.services.notification_service import notification_service
from app.crud.crud_user import user as user_crud


def run_async(coro):
    """Helper function to run async functions in sync context"""
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

    return loop.run_until_complete(coro)

logger = logging.getLogger(__name__)


def get_db() -> Session:
    """Get database session for tasks"""
    return SessionLocal()


@celery_app.task(bind=True, max_retries=3)
def send_notification_task(
    self,
    user_id: str,
    template_key: str,
    variables: Optional[Dict[str, Any]] = None,
    language: Optional[str] = None
):
    """
    Background task to send notification to a single user
    
    Args:
        user_id: Target user ID
        template_key: Notification template key
        variables: Template variables
        language: Preferred language
    """
    try:
        db = get_db()
        
        result = run_async(notification_service.send_notification_by_template(
            db=db,
            user_id=UUID(user_id),
            template_key=template_key,
            variables=variables or {},
            language=language
        ))
        
        db.close()
        
        if result["success"]:
            logger.info(f"Notification sent successfully to user {user_id}")
            return {
                "success": True,
                "user_id": user_id,
                "sent_count": result["sent_count"]
            }
        else:
            logger.warning(f"Failed to send notification to user {user_id}: {result.get('error')}")
            return {
                "success": False,
                "user_id": user_id,
                "error": result.get("error")
            }
            
    except Exception as exc:
        logger.error(f"Notification task failed for user {user_id}: {exc}")
        
        # Retry with exponential backoff
        try:
            raise self.retry(countdown=60 * (2 ** self.request.retries))
        except self.MaxRetriesExceededError:
            logger.error(f"Max retries exceeded for notification to user {user_id}")
            return {
                "success": False,
                "user_id": user_id,
                "error": "Max retries exceeded"
            }


@celery_app.task(bind=True)
def send_batch_notification_task(
    self,
    user_ids: List[str],
    template_key: str,
    variables: Optional[Dict[str, Any]] = None,
    language: Optional[str] = None
):
    """
    Background task to send notifications to multiple users
    
    Args:
        user_ids: List of target user IDs
        template_key: Notification template key
        variables: Template variables
        language: Preferred language
    """
    try:
        db = get_db()
        
        # Convert string UUIDs to UUID objects
        uuid_list = [UUID(uid) for uid in user_ids]
        
        result = run_async(notification_service.send_batch_notification(
            db=db,
            user_ids=uuid_list,
            template_key=template_key,
            variables=variables or {},
            language=language
        ))
        
        db.close()
        
        logger.info(f"Batch notification completed: {result['success_users']}/{result['total_users']} successful")
        
        return {
            "success": result["success"],
            "total_users": result["total_users"],
            "success_users": result["success_users"],
            "template_key": template_key
        }
        
    except Exception as exc:
        logger.error(f"Batch notification task failed: {exc}")
        return {
            "success": False,
            "error": str(exc),
            "template_key": template_key
        }


@celery_app.task
def send_welcome_notification_task(user_id: str):
    """Send welcome notification to new user"""
    try:
        db = get_db()
        
        user = user_crud.get(db, id=UUID(user_id))
        if not user:
            logger.error(f"User not found: {user_id}")
            return {"success": False, "error": "User not found"}
        
        variables = {
            "user_name": user.display_name or "bạn",
            "app_name": "Bana Chef"
        }
        
        result = run_async(notification_service.send_notification_by_template(
            db=db,
            user_id=UUID(user_id),
            template_key="welcome_new_user",
            variables=variables
        ))
        
        db.close()
        
        logger.info(f"Welcome notification sent to user {user_id}")
        return result
        
    except Exception as exc:
        logger.error(f"Welcome notification task failed: {exc}")
        return {"success": False, "error": str(exc)}


@celery_app.task
def send_referral_reward_notification_task(
    user_id: str,
    referred_user_name: str,
    reward_amount: float = 5.0
):
    """Send referral reward notification"""
    try:
        db = get_db()
        
        variables = {
            "referred_user_name": referred_user_name,
            "reward_amount": reward_amount
        }
        
        result = run_async(notification_service.send_notification_by_template(
            db=db,
            user_id=UUID(user_id),
            template_key="referral_reward",
            variables=variables
        ))
        
        db.close()
        
        logger.info(f"Referral reward notification sent to user {user_id}")
        return result
        
    except Exception as exc:
        logger.error(f"Referral reward notification task failed: {exc}")
        return {"success": False, "error": str(exc)}


@celery_app.task
def send_recipe_suggestion_task(user_id: str, recipe_name: str, recipe_id: str):
    """Send recipe suggestion notification"""
    try:
        db = get_db()
        
        variables = {
            "recipe_name": recipe_name,
            "recipe_id": recipe_id
        }
        
        result = run_async(notification_service.send_notification_by_template(
            db=db,
            user_id=UUID(user_id),
            template_key="recipe_suggestion",
            variables=variables
        ))
        
        db.close()
        
        logger.info(f"Recipe suggestion sent to user {user_id}")
        return result
        
    except Exception as exc:
        logger.error(f"Recipe suggestion task failed: {exc}")
        return {"success": False, "error": str(exc)}


@celery_app.task(bind=True)
def cleanup_failed_notifications_task(self):
    """Clean up failed notification logs"""
    try:
        db = get_db()
        
        # TODO: Implement cleanup logic
        # - Remove old failed notifications
        # - Deactivate invalid device tokens
        # - Update statistics
        
        db.close()
        
        logger.info("Notification cleanup completed")
        return {"success": True}
        
    except Exception as exc:
        logger.error(f"Cleanup task failed: {exc}")
        return {"success": False, "error": str(exc)}


# Helper functions for triggering notifications
def trigger_welcome_notification(user_id: UUID):
    """Trigger welcome notification (async)"""
    send_welcome_notification_task.delay(str(user_id))


def trigger_referral_reward_notification(
    user_id: UUID,
    referred_user_name: str,
    reward_amount: float = 5.0
):
    """Trigger referral reward notification (async)"""
    send_referral_reward_notification_task.delay(
        str(user_id),
        referred_user_name,
        reward_amount
    )


def trigger_recipe_suggestion(user_id: UUID, recipe_name: str, recipe_id: UUID):
    """Trigger recipe suggestion notification (async)"""
    send_recipe_suggestion_task.delay(
        str(user_id),
        recipe_name,
        str(recipe_id)
    )
