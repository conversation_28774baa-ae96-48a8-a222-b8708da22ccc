"""
Scheduled Celery tasks for periodic notifications.
"""

import logging
import asyncio
from datetime import datetime, timed<PERSON><PERSON>
from typing import List
from sqlalchemy.orm import Session
from sqlalchemy import and_, func

from app.core.celery_app import celery_app
from app.core.config import settings
from app.db.session import SessionLocal
from app.models.user import User
from app.models.device_token import DeviceToken
from app.models.notification_log import NotificationLog
from app.services.notification_service import notification_service
from app.crud.crud_user import user as user_crud

logger = logging.getLogger(__name__)


def run_async(coro):
    """Helper function to run async functions in sync context"""
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

    return loop.run_until_complete(coro)


def get_db() -> Session:
    """Get database session for tasks"""
    return SessionLocal()


@celery_app.task
def send_daily_recipe_suggestions():
    """Send daily recipe suggestions to active users"""
    try:
        db = get_db()
        
        # Get users who have active device tokens and haven't received suggestions today
        today = datetime.utcnow().date()
        
        # Find users with active devices who haven't received recipe suggestions today
        users_query = db.query(User).join(DeviceToken).filter(
            and_(
                DeviceToken.is_active == True,
                ~User.user_id.in_(
                    db.query(NotificationLog.user_id).filter(
                        and_(
                            NotificationLog.notification_type == "recipe_suggestion",
                            func.date(NotificationLog.created_at) == today
                        )
                    )
                )
            )
        ).distinct()
        
        users = users_query.limit(100).all()  # Limit to prevent overwhelming
        
        sent_count = 0
        for user in users:
            # TODO: Get personalized recipe suggestion based on user profile
            # For now, use a generic suggestion
            variables = {
                "recipe_name": "Phở Gà Nấm Hương",
                "recipe_id": "sample-recipe-id"
            }
            
            result = run_async(notification_service.send_notification_by_template(
                db=db,
                user_id=user.user_id,
                template_key="recipe_suggestion",
                variables=variables
            ))
            
            if result["success"]:
                sent_count += 1
        
        db.close()
        
        logger.info(f"Daily recipe suggestions sent to {sent_count} users")
        return {
            "success": True,
            "sent_count": sent_count,
            "total_eligible": len(users)
        }
        
    except Exception as exc:
        logger.error(f"Daily recipe suggestions task failed: {exc}")
        return {"success": False, "error": str(exc)}


@celery_app.task
def send_weekly_cooking_summary():
    """Send weekly cooking summary to users"""
    try:
        db = get_db()
        
        # Get users who have been active in the past week
        week_ago = datetime.utcnow() - timedelta(days=7)
        
        active_users = db.query(User).join(DeviceToken).filter(
            and_(
                DeviceToken.is_active == True,
                DeviceToken.last_used_at >= week_ago
            )
        ).distinct().limit(50).all()  # Limit for now
        
        sent_count = 0
        for user in active_users:
            # TODO: Generate actual cooking summary based on user activity
            variables = {
                "user_name": user.display_name or "bạn",
                "recipes_tried": 3,
                "favorite_cuisine": "Việt Nam",
                "week_start": (datetime.utcnow() - timedelta(days=7)).strftime("%d/%m"),
                "week_end": datetime.utcnow().strftime("%d/%m")
            }
            
            # Use a generic template for now
            result = run_async(notification_service.send_notification_by_template(
                db=db,
                user_id=user.user_id,
                template_key="weekly_summary",  # Need to create this template
                variables=variables
            ))
            
            if result["success"]:
                sent_count += 1
        
        db.close()
        
        logger.info(f"Weekly summaries sent to {sent_count} users")
        return {
            "success": True,
            "sent_count": sent_count,
            "total_eligible": len(active_users)
        }
        
    except Exception as exc:
        logger.error(f"Weekly summary task failed: {exc}")
        return {"success": False, "error": str(exc)}


@celery_app.task
def send_reactivation_notifications():
    """Send reactivation notifications to inactive users"""
    try:
        db = get_db()
        
        # Find users who haven't been active for 7+ days but have device tokens
        inactive_threshold = datetime.utcnow() - timedelta(days=7)
        
        inactive_users = db.query(User).join(DeviceToken).filter(
            and_(
                DeviceToken.is_active == True,
                User.last_login_at < inactive_threshold,
                # Don't send if already sent reactivation in last 3 days
                ~User.user_id.in_(
                    db.query(NotificationLog.user_id).filter(
                        and_(
                            NotificationLog.notification_type == "reactivation",
                            NotificationLog.created_at >= datetime.utcnow() - timedelta(days=3)
                        )
                    )
                )
            )
        ).distinct().limit(30).all()  # Limit to prevent spam
        
        sent_count = 0
        for user in inactive_users:
            variables = {
                "user_name": user.display_name or "bạn",
                "days_away": (datetime.utcnow() - user.last_login_at).days
            }
            
            # TODO: Create reactivation template
            result = run_async(notification_service.send_notification_by_template(
                db=db,
                user_id=user.user_id,
                template_key="user_reactivation",  # Need to create this template
                variables=variables
            ))
            
            if result["success"]:
                sent_count += 1
        
        db.close()
        
        logger.info(f"Reactivation notifications sent to {sent_count} users")
        return {
            "success": True,
            "sent_count": sent_count,
            "total_eligible": len(inactive_users)
        }
        
    except Exception as exc:
        logger.error(f"Reactivation notifications task failed: {exc}")
        return {"success": False, "error": str(exc)}


@celery_app.task
def cleanup_old_notification_logs():
    """Clean up old notification logs and inactive device tokens"""
    try:
        db = get_db()
        
        # Remove notification logs older than 30 days
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)
        
        old_logs_count = db.query(NotificationLog).filter(
            NotificationLog.created_at < thirty_days_ago
        ).count()
        
        db.query(NotificationLog).filter(
            NotificationLog.created_at < thirty_days_ago
        ).delete()
        
        # Remove inactive device tokens older than 60 days
        sixty_days_ago = datetime.utcnow() - timedelta(days=60)
        
        old_tokens_count = db.query(DeviceToken).filter(
            and_(
                DeviceToken.is_active == False,
                DeviceToken.updated_at < sixty_days_ago
            )
        ).count()
        
        db.query(DeviceToken).filter(
            and_(
                DeviceToken.is_active == False,
                DeviceToken.updated_at < sixty_days_ago
            )
        ).delete()
        
        db.commit()
        db.close()
        
        logger.info(f"Cleanup completed: {old_logs_count} logs, {old_tokens_count} tokens removed")
        return {
            "success": True,
            "logs_removed": old_logs_count,
            "tokens_removed": old_tokens_count
        }
        
    except Exception as exc:
        logger.error(f"Cleanup task failed: {exc}")
        return {"success": False, "error": str(exc)}


@celery_app.task
def validate_device_tokens():
    """Validate device tokens and deactivate invalid ones"""
    try:
        db = get_db()
        
        # Get active device tokens that haven't been validated recently
        validation_threshold = datetime.utcnow() - timedelta(days=7)
        
        tokens_to_validate = db.query(DeviceToken).filter(
            and_(
                DeviceToken.is_active == True,
                DeviceToken.last_used_at < validation_threshold
            )
        ).limit(100).all()  # Limit to prevent rate limiting
        
        deactivated_count = 0
        
        for token in tokens_to_validate:
            # TODO: Implement token validation with Firebase
            # For now, just update last_used_at
            token.last_used_at = datetime.utcnow()
            db.add(token)
        
        db.commit()
        db.close()
        
        logger.info(f"Token validation completed: {deactivated_count} tokens deactivated")
        return {
            "success": True,
            "validated_count": len(tokens_to_validate),
            "deactivated_count": deactivated_count
        }
        
    except Exception as exc:
        logger.error(f"Token validation task failed: {exc}")
        return {"success": False, "error": str(exc)}
