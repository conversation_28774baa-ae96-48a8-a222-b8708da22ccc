from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, EmailStr
from uuid import UUID

from .user_profile import UserProfile
from .user_temporary_state import UserTemporaryState


class UserBase(BaseModel):
    email: EmailStr
    display_name: Optional[str] = None
    photo_url: Optional[str] = None
    referral_code: Optional[str] = None


class UserCreate(UserBase):
    password: Optional[str] = None
    auth_provider: str
    provider_user_id: Optional[str] = None


class UserUpdate(BaseModel):
    email: Optional[EmailStr] = None
    display_name: Optional[str] = None
    photo_url: Optional[str] = None
    password: Optional[str] = None


class UserInDBBase(UserBase):
    user_id: UUID
    auth_provider: str
    provider_user_id: Optional[str] = None
    referred_by_user_id: Optional[UUID] = None
    has_made_first_purchase: bool = False
    created_at: datetime
    updated_at: datetime
    last_login_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class User(UserInDBBase):
    profile: Optional[UserProfile] = None
    temporary_states: Optional[List[UserTemporaryState]] = None


class UserInDB(UserInDBBase):
    password_hash: Optional[str] = None