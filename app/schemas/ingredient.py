from typing import Optional
from datetime import datetime
from pydantic import BaseModel
from uuid import UUID


class IngredientBase(BaseModel):
    name: str
    description: Optional[str] = None
    default_unit: Optional[str] = None


class IngredientCreate(IngredientBase):
    pass


class IngredientUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    default_unit: Optional[str] = None


class IngredientInDBBase(IngredientBase):
    ingredient_id: UUID
    created_at: datetime

    class Config:
        from_attributes = True


class Ingredient(IngredientInDBBase):
    pass


class IngredientInDB(IngredientInDBBase):
    pass
