from typing import Optional
from decimal import Decimal
from pydantic import BaseModel
from uuid import UUID

from .ingredient import Ingredient


class RecipeIngredientBase(BaseModel):
    quantity: Decimal
    unit: str
    notes: Optional[str] = None


class RecipeIngredientCreate(RecipeIngredientBase):
    recipe_id: UUID
    ingredient_id: UUID


class RecipeIngredientUpdate(BaseModel):
    quantity: Optional[Decimal] = None
    unit: Optional[str] = None
    notes: Optional[str] = None


class RecipeIngredientInDBBase(RecipeIngredientBase):
    recipe_ingredient_id: UUID
    recipe_id: UUID
    ingredient_id: UUID

    class Config:
        from_attributes = True


class RecipeIngredient(RecipeIngredientInDBBase):
    ingredient: Optional[Ingredient] = None


class RecipeIngredientInDB(RecipeIngredientInDBBase):
    pass
