"""
Pydantic schemas for device tokens.
"""

from typing import Optional
from datetime import datetime
from pydantic import BaseModel, Field
from uuid import UUID


class DeviceTokenBase(BaseModel):
    """Base device token schema"""
    device_token: str = Field(..., description="FCM device token")
    device_type: str = Field(..., description="Device type: ios, android, web")
    device_id: Optional[str] = Field(None, description="Unique device identifier")
    app_version: Optional[str] = Field(None, description="App version")
    os_version: Optional[str] = Field(None, description="OS version")


class DeviceTokenCreate(DeviceTokenBase):
    """Schema for creating device token"""
    pass


class DeviceTokenUpdate(BaseModel):
    """Schema for updating device token"""
    device_token: Optional[str] = None
    device_type: Optional[str] = None
    device_id: Optional[str] = None
    app_version: Optional[str] = None
    os_version: Optional[str] = None
    is_active: Optional[bool] = None


class DeviceTokenRegister(BaseModel):
    """Schema for device token registration"""
    device_token: str = Field(..., description="FCM device token")
    device_type: str = Field(..., description="Device type: ios, android, web")
    device_id: Optional[str] = Field(None, description="Unique device identifier")
    app_version: Optional[str] = Field(None, description="App version")
    os_version: Optional[str] = Field(None, description="OS version")
    
    class Config:
        json_schema_extra = {
            "example": {
                "device_token": "fGhJ8K9L0mN1oP2qR3sT4uV5wX6yZ7aB8cD9eF0gH1iJ2kL3mN4oP5qR6sT7uV8wX9yZ0",
                "device_type": "ios",
                "device_id": "iPhone14,2",
                "app_version": "1.0.0",
                "os_version": "16.0"
            }
        }


class DeviceTokenInDB(DeviceTokenBase):
    """Schema for device token in database"""
    token_id: UUID
    user_id: UUID
    is_active: bool
    last_used_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class DeviceToken(DeviceTokenInDB):
    """Public device token schema"""
    pass


class DeviceTokenResponse(BaseModel):
    """Response schema for device token operations"""
    success: bool
    message: str
    token_id: Optional[UUID] = None
    
    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "Device token registered successfully",
                "token_id": "123e4567-e89b-12d3-a456-************"
            }
        }


class DeviceTokenListResponse(BaseModel):
    """Response schema for device token list"""
    success: bool
    devices: list[DeviceToken]
    total: int
    
    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "total": 2,
                "devices": [
                    {
                        "token_id": "123e4567-e89b-12d3-a456-************",
                        "user_id": "123e4567-e89b-12d3-a456-************",
                        "device_token": "fGhJ8K9L0mN1oP2qR3sT4uV5wX6yZ7aB8cD9eF0gH1iJ2kL3mN4oP5qR6sT7uV8wX9yZ0",
                        "device_type": "ios",
                        "device_id": "iPhone14,2",
                        "app_version": "1.0.0",
                        "os_version": "16.0",
                        "is_active": True,
                        "last_used_at": "2024-01-15T10:30:00Z",
                        "created_at": "2024-01-15T10:30:00Z",
                        "updated_at": "2024-01-15T10:30:00Z"
                    }
                ]
            }
        }


class NotificationTestRequest(BaseModel):
    """Schema for testing notification"""
    title: str = Field(..., description="Notification title")
    body: str = Field(..., description="Notification body")
    device_token_id: Optional[UUID] = Field(None, description="Specific device token ID (optional)")
    
    class Config:
        json_schema_extra = {
            "example": {
                "title": "Test Notification",
                "body": "This is a test notification from Bana Chef!",
                "device_token_id": "123e4567-e89b-12d3-a456-************"
            }
        }
