from typing import Optional, List
from datetime import date, datetime
from pydantic import BaseModel, Field
from uuid import UUID


class UserProfileBase(BaseModel):
    # Thông Tin Sức Khỏe & <PERSON><PERSON><PERSON> (Mục 1)
    date_of_birth: Optional[date] = None
    gender: Optional[str] = None
    weight_kg: Optional[float] = None
    height_cm: Optional[float] = None
    activity_level: Optional[str] = None
    health_goals: Optional[List[str]] = None
    food_allergies: Optional[List[str]] = None
    medical_conditions: Optional[List[str]] = None
    is_pregnant_or_breastfeeding: Optional[bool] = False

    # Thông Tin <PERSON>ẩ<PERSON> Vị & Sở <PERSON>h<PERSON><PERSON> (Mục 2)
    sweet_preference_level: Optional[int] = Field(None, ge=1, le=5)
    salty_preference_level: Optional[int] = Field(None, ge=1, le=5)
    sour_preference_level: Optional[int] = Field(None, ge=1, le=5)
    bitter_preference_level: Optional[int] = Field(None, ge=1, le=5)
    umami_preference_level: Optional[int] = Field(None, ge=1, le=5)
    favorite_foods_ingredients: Optional[List[str]] = None
    disliked_foods_ingredients: Optional[List[str]] = None
    spice_level_preference: Optional[str] = None
    texture_preferences: Optional[List[str]] = None
    typical_meals_description: Optional[str] = None

    # Thông Tin Văn Hóa & Lối Sống (Mục 3)
    country_region_of_residence: Optional[str] = None
    preferred_cuisines: Optional[List[str]] = None
    religious_dietary_restrictions: Optional[List[str]] = None
    eating_lifestyle: Optional[str] = None

    # Thông Tin Kỹ Năng & Điều Kiện Nấu Nướng (Mục 4)
    cooking_skill_level: Optional[str] = None
    avg_time_for_main_meal_prep_cook_minutes: Optional[int] = None
    available_kitchen_equipment: Optional[List[str]] = None
    grocery_budget_range: Optional[str] = None
    fresh_ingredients_access_level: Optional[str] = None

    # Thông Tin về Mức độ "Phiêu lưu" khi Ăn uống (Mục 5)
    adventure_level_new_cuisines: Optional[str] = None
    recipe_complexity_preference: Optional[str] = None


class UserProfileCreate(UserProfileBase):
    user_id: UUID


class UserProfileUpdate(UserProfileBase):
    pass


class UserProfileInDBBase(UserProfileBase):
    profile_id: UUID
    user_id: UUID
    updated_at: datetime

    class Config:
        from_attributes = True


class UserProfile(UserProfileInDBBase):
    pass


class UserProfileInDB(UserProfileInDBBase):
    pass
