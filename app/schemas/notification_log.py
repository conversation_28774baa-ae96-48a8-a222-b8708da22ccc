"""
Pydantic schemas for notification logs and analytics.
"""

from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel, Field
from uuid import UUID


class NotificationLogBase(BaseModel):
    """Base notification log schema"""
    user_id: UUID
    device_token_id: Optional[UUID] = None
    template_id: Optional[UUID] = None
    title: str
    body: str
    image_url: Optional[str] = None
    action_url: Optional[str] = None
    notification_type: str
    category: str
    priority: str = "normal"
    language: str = "vi"
    template_variables: Optional[Dict[str, Any]] = None
    delivery_status: str = "pending"
    delivery_error: Optional[str] = None
    scheduled_at: Optional[datetime] = None


class NotificationLogCreate(NotificationLogBase):
    """Schema for creating notification log"""
    pass


class NotificationLogUpdate(BaseModel):
    """Schema for updating notification log"""
    delivery_status: Optional[str] = None
    delivery_error: Optional[str] = None
    firebase_message_id: Optional[str] = None
    sent_at: Optional[datetime] = None
    delivered_at: Optional[datetime] = None
    clicked_at: Optional[datetime] = None


class NotificationLogInDB(NotificationLogBase):
    """Schema for notification log in database"""
    log_id: UUID
    firebase_message_id: Optional[str] = None
    sent_at: Optional[datetime] = None
    delivered_at: Optional[datetime] = None
    clicked_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class NotificationLog(NotificationLogInDB):
    """Public notification log schema"""
    pass


class NotificationAnalytics(BaseModel):
    """Notification analytics response"""
    total_notifications: int
    sent_count: int
    success_rate: float
    click_rate: float
    status_breakdown: Dict[str, int]
    
    class Config:
        json_schema_extra = {
            "example": {
                "total_notifications": 1000,
                "sent_count": 950,
                "success_rate": 95.0,
                "click_rate": 15.5,
                "status_breakdown": {
                    "sent": 800,
                    "delivered": 100,
                    "clicked": 50,
                    "failed": 50
                }
            }
        }


class TemplatePerformance(BaseModel):
    """Template performance metrics"""
    notification_type: str
    total: int
    sent: int
    clicked: int
    success_rate: float
    click_rate: float
    
    class Config:
        json_schema_extra = {
            "example": {
                "notification_type": "welcome",
                "total": 100,
                "sent": 95,
                "clicked": 20,
                "success_rate": 95.0,
                "click_rate": 21.05
            }
        }


class DailyStats(BaseModel):
    """Daily notification statistics"""
    date: str
    total: int
    sent: int
    clicked: int
    success_rate: float
    click_rate: float
    
    class Config:
        json_schema_extra = {
            "example": {
                "date": "2024-01-15",
                "total": 50,
                "sent": 48,
                "clicked": 8,
                "success_rate": 96.0,
                "click_rate": 16.67
            }
        }


class UserEngagement(BaseModel):
    """User engagement metrics"""
    user_id: str
    period_days: int
    total_notifications: int
    clicked_notifications: int
    click_rate: float
    notification_types: Dict[str, int]
    
    class Config:
        json_schema_extra = {
            "example": {
                "user_id": "123e4567-e89b-12d3-a456-************",
                "period_days": 30,
                "total_notifications": 15,
                "clicked_notifications": 3,
                "click_rate": 20.0,
                "notification_types": {
                    "welcome": 1,
                    "recipe_suggestion": 10,
                    "referral_reward": 2,
                    "weekly_summary": 2
                }
            }
        }


class NotificationAnalyticsResponse(BaseModel):
    """Response schema for notification analytics"""
    success: bool
    analytics: NotificationAnalytics
    
    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "analytics": {
                    "total_notifications": 1000,
                    "sent_count": 950,
                    "success_rate": 95.0,
                    "click_rate": 15.5,
                    "status_breakdown": {
                        "sent": 800,
                        "delivered": 100,
                        "clicked": 50,
                        "failed": 50
                    }
                }
            }
        }


class TemplatePerformanceResponse(BaseModel):
    """Response schema for template performance"""
    success: bool
    templates: List[TemplatePerformance]
    
    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "templates": [
                    {
                        "notification_type": "welcome",
                        "total": 100,
                        "sent": 95,
                        "clicked": 20,
                        "success_rate": 95.0,
                        "click_rate": 21.05
                    }
                ]
            }
        }


class DailyStatsResponse(BaseModel):
    """Response schema for daily statistics"""
    success: bool
    stats: List[DailyStats]
    
    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "stats": [
                    {
                        "date": "2024-01-15",
                        "total": 50,
                        "sent": 48,
                        "clicked": 8,
                        "success_rate": 96.0,
                        "click_rate": 16.67
                    }
                ]
            }
        }


class UserEngagementResponse(BaseModel):
    """Response schema for user engagement"""
    success: bool
    engagement: UserEngagement
    
    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "engagement": {
                    "user_id": "123e4567-e89b-12d3-a456-************",
                    "period_days": 30,
                    "total_notifications": 15,
                    "clicked_notifications": 3,
                    "click_rate": 20.0,
                    "notification_types": {
                        "welcome": 1,
                        "recipe_suggestion": 10,
                        "referral_reward": 2,
                        "weekly_summary": 2
                    }
                }
            }
        }


class NotificationClickRequest(BaseModel):
    """Schema for notification click tracking"""
    log_id: UUID = Field(..., description="Notification log ID")
    
    class Config:
        json_schema_extra = {
            "example": {
                "log_id": "123e4567-e89b-12d3-a456-************"
            }
        }


class NotificationClickResponse(BaseModel):
    """Response schema for notification click tracking"""
    success: bool
    message: str
    
    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "Click tracked successfully"
            }
        }
