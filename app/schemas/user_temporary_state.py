from typing import Optional
from datetime import datetime
from pydantic import BaseModel
from uuid import UUID


class UserTemporaryStateBase(BaseModel):
    state_tag: str
    state_description: Optional[str] = None
    applies_from: Optional[datetime] = None
    applies_until: Optional[datetime] = None
    is_active: bool = True


class UserTemporaryStateCreate(UserTemporaryStateBase):
    user_id: UUID


class UserTemporaryStateUpdate(BaseModel):
    state_tag: Optional[str] = None
    state_description: Optional[str] = None
    applies_from: Optional[datetime] = None
    applies_until: Optional[datetime] = None
    is_active: Optional[bool] = None


class UserTemporaryStateInDBBase(UserTemporaryStateBase):
    state_id: UUID
    user_id: UUID
    applies_from: datetime
    created_at: datetime

    class Config:
        from_attributes = True


class UserTemporaryState(UserTemporaryStateInDBBase):
    pass


class UserTemporaryStateInDB(UserTemporaryStateInDBBase):
    pass
