from pydantic import BaseModel


class FirebaseLoginRequest(BaseModel):
    firebase_token: str


class GoogleLoginRequest(BaseModel):
    token: str


class AppleLoginRequest(BaseModel):
    token: str


class RefreshTokenRequest(BaseModel):
    refresh_token: str


class RefreshTokenResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"


class LoginResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    user: dict  # User information


class TokenVerificationRequest(BaseModel):
    token: str


class TokenVerificationResponse(BaseModel):
    valid: bool
    user_id: str = None
    token_type: str = None  # "jwt" or "firebase"
