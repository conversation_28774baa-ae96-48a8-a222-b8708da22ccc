from typing import Optional, List, Dict, Any
from datetime import datetime
from decimal import Decimal
from pydantic import BaseModel
from uuid import UUID

from .recipe_ingredient import RecipeIngredient


class RecipeBase(BaseModel):
    name: str
    description: Optional[str] = None
    instructions: str
    prep_time_minutes: Optional[int] = None
    cook_time_minutes: Optional[int] = None
    servings: int = 1

    # Dinh dưỡng ước tính cho mỗi khẩu phần
    calories_per_serving: Optional[int] = None
    protein_g_per_serving: Optional[Decimal] = None
    carbs_g_per_serving: Optional[Decimal] = None
    fat_g_per_serving: Optional[Decimal] = None

    # Dinh dưỡng và đánh giá ước tính cho toàn bộ món ăn
    total_dish_nutrient_summary: Optional[str] = None
    total_dish_heart_health_indicators: Optional[Dict[str, Any]] = None
    controlled_consumption_suitability: Optional[str] = None
    total_dish_vitamins: Optional[Dict[str, Any]] = None
    total_dish_minerals: Optional[Dict[str, Any]] = None
    dish_effects_and_notes: Optional[str] = None

    # Thông tin khác
    image_url: Optional[str] = None
    source: Optional[str] = None


class RecipeCreate(RecipeBase):
    pass


class RecipeUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    instructions: Optional[str] = None
    prep_time_minutes: Optional[int] = None
    cook_time_minutes: Optional[int] = None
    servings: Optional[int] = None
    calories_per_serving: Optional[int] = None
    protein_g_per_serving: Optional[Decimal] = None
    carbs_g_per_serving: Optional[Decimal] = None
    fat_g_per_serving: Optional[Decimal] = None
    total_dish_nutrient_summary: Optional[str] = None
    total_dish_heart_health_indicators: Optional[Dict[str, Any]] = None
    controlled_consumption_suitability: Optional[str] = None
    total_dish_vitamins: Optional[Dict[str, Any]] = None
    total_dish_minerals: Optional[Dict[str, Any]] = None
    dish_effects_and_notes: Optional[str] = None
    image_url: Optional[str] = None
    source: Optional[str] = None


class RecipeInDBBase(RecipeBase):
    recipe_id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class Recipe(RecipeInDBBase):
    recipe_ingredients: Optional[List[RecipeIngredient]] = None


class RecipeInDB(RecipeInDBBase):
    pass