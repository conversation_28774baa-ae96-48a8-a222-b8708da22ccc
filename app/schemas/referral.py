from typing import Optional, List
from datetime import datetime
from decimal import Decimal
from pydantic import BaseModel, Field
from uuid import UUID


# Referral Voucher Schemas
class ReferralVoucherBase(BaseModel):
    amount_usd: Decimal = Field(default=Decimal("5.00"), description="Voucher amount in USD")
    status: str = Field(default="available", description="Voucher status: available, used, expired")
    expires_at: Optional[datetime] = None


class ReferralVoucherCreate(ReferralVoucherBase):
    owner_user_id: UUID
    source_referral_id: UUID


class ReferralVoucherUpdate(BaseModel):
    status: Optional[str] = None
    used_at: Optional[datetime] = None


class ReferralVoucherInDBBase(ReferralVoucherBase):
    voucher_id: UUID
    owner_user_id: UUID
    source_referral_id: UUID
    created_at: datetime
    used_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class ReferralVoucher(ReferralVoucherInDBBase):
    pass


class ReferralVoucherInDB(ReferralVoucherInDBBase):
    pass


# Referral History Schemas
class ReferralHistoryBase(BaseModel):
    status: str = Field(default="pending", description="Referral status: pending, completed")


class ReferralHistoryCreate(ReferralHistoryBase):
    referrer_user_id: UUID
    referred_user_id: UUID


class ReferralHistoryUpdate(BaseModel):
    status: Optional[str] = None


class ReferralHistoryInDBBase(ReferralHistoryBase):
    id: UUID
    referrer_user_id: UUID
    referred_user_id: UUID
    created_at: datetime

    class Config:
        from_attributes = True


class ReferralHistory(ReferralHistoryInDBBase):
    pass


class ReferralHistoryInDB(ReferralHistoryInDBBase):
    pass


# API Request/Response Schemas
class ApplyReferralCodeRequest(BaseModel):
    referral_code: str = Field(..., description="Referral code to apply")


class ApplyReferralCodeResponse(BaseModel):
    success: bool
    message: str
    referrer_display_name: Optional[str] = None


class SubscriptionOffering(BaseModel):
    package_id: str
    package_name: str
    base_price_usd: Decimal
    final_price_usd: Decimal
    discount_amount_usd: Decimal = Decimal("0.00")


class PurchaseOfferingsResponse(BaseModel):
    offerings: List[SubscriptionOffering]
    applied_vouchers: List[UUID] = []
    new_user_discount_applied: bool = False
    total_discount_usd: Decimal = Decimal("0.00")


class WebhookPaymentData(BaseModel):
    user_id: UUID
    package_id: str
    amount_paid_usd: Decimal
    applied_vouchers: List[UUID] = []
    transaction_id: str
    payment_provider: str  # "google_play" or "app_store"


class ReferralStatsResponse(BaseModel):
    referral_code: str
    total_referrals: int
    completed_referrals: int
    pending_referrals: int
    available_vouchers: int
    total_voucher_value_usd: Decimal
