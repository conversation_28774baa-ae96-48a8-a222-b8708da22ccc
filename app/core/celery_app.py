"""
Celery configuration for background tasks and scheduled notifications.
"""

from celery import Celery
from app.core.config import settings

# Create Celery instance
celery_app = Celery(
    "banachef",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND,
    include=[
        "app.tasks.notification_tasks",
        "app.tasks.scheduled_tasks"
    ]
)

# Celery configuration
celery_app.conf.update(
    # Task routing
    task_routes={
        "app.tasks.notification_tasks.*": {"queue": "notifications"},
        "app.tasks.scheduled_tasks.*": {"queue": "scheduled"},
    },
    
    # Task serialization
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    
    # Timezone
    timezone="UTC",
    enable_utc=True,
    
    # Task execution
    task_acks_late=True,
    worker_prefetch_multiplier=1,
    
    # Task retry configuration
    task_default_retry_delay=60,  # 1 minute
    task_max_retries=3,
    
    # Result backend settings
    result_expires=3600,  # 1 hour
    
    # Beat schedule for periodic tasks
    beat_schedule={
        "daily-recipe-suggestions": {
            "task": "app.tasks.scheduled_tasks.send_daily_recipe_suggestions",
            "schedule": 60.0 * 60.0 * 24.0,  # Daily
            "options": {"queue": "scheduled"}
        },
        "weekly-cooking-summary": {
            "task": "app.tasks.scheduled_tasks.send_weekly_cooking_summary", 
            "schedule": 60.0 * 60.0 * 24.0 * 7.0,  # Weekly
            "options": {"queue": "scheduled"}
        },
        "cleanup-old-notifications": {
            "task": "app.tasks.scheduled_tasks.cleanup_old_notification_logs",
            "schedule": 60.0 * 60.0 * 24.0,  # Daily
            "options": {"queue": "scheduled"}
        },
        "reactivate-inactive-users": {
            "task": "app.tasks.scheduled_tasks.send_reactivation_notifications",
            "schedule": 60.0 * 60.0 * 24.0 * 3.0,  # Every 3 days
            "options": {"queue": "scheduled"}
        }
    }
)

# Auto-discover tasks
celery_app.autodiscover_tasks()

if __name__ == "__main__":
    celery_app.start()
