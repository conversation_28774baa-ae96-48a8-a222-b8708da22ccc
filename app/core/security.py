from datetime import datetime, timedelta
from typing import Any, Union, Optional
from jose import jwt, JW<PERSON>rror
from app.core.config import settings
from app.services.firebase_auth_service import firebase_auth_service


def create_access_token(
    subject: Union[str, Any], expires_delta: timedelta = None
) -> str:
    """Create JWT access token"""
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )

    to_encode = {"exp": expire, "sub": str(subject)}
    encoded_jwt = jwt.encode(
        to_encode, settings.JWT_SECRET_KEY, algorithm=settings.JWT_ALGORITHM
    )
    return encoded_jwt


def create_refresh_token(subject: Union[str, Any]) -> str:
    """Create JWT refresh token (longer expiry)"""
    expire = datetime.utcnow() + timedelta(days=365)  
    to_encode = {"exp": expire, "sub": str(subject), "type": "refresh"}
    encoded_jwt = jwt.encode(
        to_encode, settings.JWT_SECRET_KEY, algorithm=settings.JWT_ALGORITHM
    )
    return encoded_jwt


def verify_token(token: str) -> Optional[str]:
    """Verify JWT token and return subject"""
    try:
        payload = jwt.decode(
            token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM]
        )
        user_id: str = payload.get("sub")
        if user_id is None:
            return None
        return user_id
    except JWTError:
        return None


def verify_refresh_token(token: str) -> Optional[str]:
    """Verify refresh token and return subject"""
    try:
        payload = jwt.decode(
            token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM]
        )
        user_id: str = payload.get("sub")
        token_type: str = payload.get("type")
        if user_id is None or token_type != "refresh":
            return None
        return user_id
    except JWTError:
        return None


async def verify_token_flexible(token: str) -> Optional[str]:
    """
    Verify token - supports both Firebase ID tokens and JWT tokens
    Returns user_id if valid, None otherwise
    """
    # First try to verify as JWT token (our internal tokens)
    user_id = verify_token(token)
    if user_id:
        return user_id

    # If JWT verification fails, try Firebase token
    try:
        user_info = await firebase_auth_service.verify_firebase_token(token)
        if user_info and user_info.get('uid'):
            # For Firebase tokens, we need to map Firebase UID to our user_id
            # This would require a database lookup to find the user by Firebase UID
            # For now, return the Firebase UID (this needs to be handled in the calling code)
            return user_info.get('uid')
    except Exception:
        pass

    return None


def is_firebase_token(token: str) -> bool:
    """
    Check if token is likely a Firebase token based on structure
    Firebase tokens have specific claims that differ from our internal JWTs
    """
    try:
        # Try to decode as JWT without verification
        payload = jwt.decode(token, options={"verify_signature": False})

        # Check for Firebase-specific claims first
        firebase_claims = [
            'iss',  # Firebase tokens have issuer like 'https://securetoken.google.com/project-id'
            'aud',  # Firebase tokens have audience as project-id
            'auth_time',  # Firebase-specific claim
            'firebase'  # Firebase-specific claim
        ]

        # If it has Firebase-specific claims, it's definitely a Firebase token
        if any(claim in payload for claim in firebase_claims):
            return True

        # Check issuer specifically for Firebase
        iss = payload.get('iss', '')
        if 'securetoken.google.com' in iss or 'firebase' in iss.lower():
            return True

        # Check for our internal JWT structure
        # Our tokens have minimal claims: 'sub', 'exp', and optionally 'type' for refresh tokens
        payload_keys = set(payload.keys())

        # Our access tokens have exactly: sub, exp
        if payload_keys == {'sub', 'exp'}:
            return False  # This is our internal access token

        # Our refresh tokens have exactly: sub, exp, type
        if payload_keys == {'sub', 'exp', 'type'} and payload.get('type') == 'refresh':
            return False  # This is our internal refresh token

        # If it has more claims than our simple structure, likely Firebase
        if len(payload_keys) > 3:
            return True

        # Default to Firebase token if uncertain
        return True

    except Exception:
        # If JWT decode fails completely, assume Firebase token
        return True
