from fastapi import <PERSON><PERSON><PERSON>, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from sqlalchemy import text
import redis
from celery import Celery

from app.core.config import settings
from app.db.session import get_db
from app.api.v1 import api_router

# Khởi tạo FastAPI app
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="API server for Bana Chef cooking application",
    debug=settings.DEBUG,
)

# Cấu hình CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Health check endpoint
@app.get("/")
async def root():
    return {
        "message": "Welcome to Bana Chef Server API",
        "version": settings.APP_VERSION,
        "status": "healthy"
    }


@app.get("/health")
async def health_check(db: Session = Depends(get_db)):
    """Health check endpoint with database, Redis, and Celery connectivity test"""
    health_status = {
        "service": settings.APP_NAME,
        "version": settings.APP_VERSION,
        "timestamp": None,
        "database": "unknown",
        "redis": "unknown",
        "celery": "unknown",
        "status": "unknown"
    }

    # Test database connection
    try:
        db.execute(text("SELECT 1"))
        health_status["database"] = "healthy"
    except Exception as e:
        health_status["database"] = f"unhealthy: {str(e)}"

    # Test Redis connection
    try:
        redis_client = redis.Redis.from_url(settings.REDIS_URL)
        redis_client.ping()
        health_status["redis"] = "healthy"
    except Exception as e:
        health_status["redis"] = f"unhealthy: {str(e)}"

    # Test Celery connection
    try:
        from app.core.celery_app import celery_app
        # Check if Celery broker is accessible
        inspect = celery_app.control.inspect()
        active_workers = inspect.active()

        if active_workers:
            worker_count = len(active_workers)
            health_status["celery"] = f"healthy ({worker_count} workers)"
        else:
            health_status["celery"] = "degraded (no active workers)"
    except Exception as e:
        health_status["celery"] = f"unhealthy: {str(e)}"

    # Determine overall status
    if all(status.startswith("healthy") for status in [
        health_status["database"],
        health_status["redis"],
        health_status["celery"]
    ]):
        health_status["status"] = "healthy"
    elif health_status["database"] == "healthy" and health_status["redis"] == "healthy":
        health_status["status"] = "degraded"  # Core services OK, Celery may be down
    else:
        health_status["status"] = "unhealthy"

    # Add timestamp
    from datetime import datetime
    health_status["timestamp"] = datetime.utcnow().isoformat() + "Z"

    return health_status


# Exception handlers
@app.exception_handler(404)
async def not_found_handler(request, exc):
    return JSONResponse(
        status_code=404,
        content={"detail": "Resource not found"}
    )


@app.exception_handler(500)
async def internal_error_handler(request, exc):
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )


# Include API routers
app.include_router(api_router, prefix="/api/v1")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
