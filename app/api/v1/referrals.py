from typing import List
from decimal import Decimal
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.api.deps import get_db, get_current_user
from app.models.user import User
from app.services.referral_service import referral_service
from app.crud.crud_referral import referral_voucher
from app.schemas.referral import (
    ApplyReferralCodeRequest,
    ApplyReferralCodeResponse,
    SubscriptionOffering,
    PurchaseOfferingsResponse,
    ReferralStatsResponse
)

router = APIRouter()


@router.post("/apply", response_model=ApplyReferralCodeResponse)
def apply_referral_code(
    request: ApplyReferralCodeRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Apply a referral code for the current user.
    
    Business Rules:
    - User can only apply one referral code
    - Cannot apply code after making first purchase
    - Cannot refer themselves
    """
    result = referral_service.apply_referral_code(
        db=db,
        user_id=current_user.user_id,
        referral_code=request.referral_code
    )
    
    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["message"]
        )
    
    return ApplyReferralCodeResponse(
        success=result["success"],
        message=result["message"],
        referrer_display_name=result.get("referrer_display_name")
    )


@router.get("/stats", response_model=ReferralStatsResponse)
def get_referral_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get referral statistics for the current user"""
    stats = referral_service.get_user_referral_stats(db, current_user.user_id)
    
    if not stats:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Referral code not found for user"
        )
    
    return stats


@router.get("/purchase-offerings", response_model=PurchaseOfferingsResponse)
def get_purchase_offerings(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get subscription offerings with applicable discounts.
    
    This endpoint calculates all available discounts for the user:
    - New user discount (5$ if referred and first purchase)
    - Referral voucher discount (5$ if available vouchers exist)
    """
    total_discount = Decimal("0.00")
    applied_vouchers = []
    new_user_discount_applied = False
    
    # Check for new user discount (Rule B2 & A2)
    if not current_user.has_made_first_purchase and current_user.referred_by_user_id:
        total_discount += Decimal("5.00")
        new_user_discount_applied = True
    
    # Check for referral voucher discount (Rule A1 & A2)
    available_voucher = referral_voucher.get_first_available_voucher(db, current_user.user_id)
    if available_voucher:
        total_discount += available_voucher.amount_usd
        applied_vouchers.append(available_voucher.voucher_id)
    
    # Define subscription packages
    base_offerings = [
        {
            "package_id": "weekly",
            "package_name": "Weekly Premium",
            "base_price_usd": Decimal("5.00")
        },
        {
            "package_id": "monthly", 
            "package_name": "Monthly Premium",
            "base_price_usd": Decimal("15.00")
        },
        {
            "package_id": "yearly",
            "package_name": "Yearly Premium", 
            "base_price_usd": Decimal("50.00")
        }
    ]
    
    # Apply discounts to offerings
    offerings = []
    for offering in base_offerings:
        discount_amount = min(total_discount, offering["base_price_usd"])
        final_price = max(Decimal("0.00"), offering["base_price_usd"] - discount_amount)
        
        offerings.append(SubscriptionOffering(
            package_id=offering["package_id"],
            package_name=offering["package_name"],
            base_price_usd=offering["base_price_usd"],
            final_price_usd=final_price,
            discount_amount_usd=discount_amount
        ))
    
    return PurchaseOfferingsResponse(
        offerings=offerings,
        applied_vouchers=applied_vouchers,
        new_user_discount_applied=new_user_discount_applied,
        total_discount_usd=total_discount
    )
