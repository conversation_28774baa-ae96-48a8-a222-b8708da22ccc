"""
API endpoints for push notifications and device token management.
"""

import logging
from typing import List, Optional
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.api.deps import get_db, get_current_user
from app.models.user import User
from app.crud.crud_device_token import device_token
from app.schemas.device_token import (
    DeviceTokenRegister,
    DeviceTokenResponse,
    DeviceTokenListResponse,
    NotificationTestRequest
)
from app.schemas.notification_template import (
    NotificationTemplateCreate,
    NotificationTemplateUpdate,
    NotificationTemplateResponse,
    NotificationTemplateListResponse,
    SendNotificationRequest,
    SendNotificationResponse,
    TEMPLATE_EXAMPLES
)
from app.crud.crud_notification_template import notification_template
from app.crud.crud_notification_log import notification_log
from app.schemas.notification_log import (
    NotificationAnalyticsResponse,
    TemplatePerformanceResponse,
    DailyStatsResponse,
    UserEngagementResponse,
    NotificationClickRequest,
    NotificationClickResponse
)
from app.services.notification_service import notification_service
from app.services.firebase_service import NotificationPayload

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/devices/register", response_model=DeviceTokenResponse)
async def register_device_token(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    device_data: DeviceTokenRegister
) -> DeviceTokenResponse:
    """
    Register or update device token for push notifications
    
    This endpoint handles:
    - New device registration
    - Token refresh for existing devices
    - Automatic cleanup of old/invalid tokens
    """
    try:
        # Validate device type
        valid_device_types = ["ios", "android", "web"]
        if device_data.device_type not in valid_device_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid device_type. Must be one of: {valid_device_types}"
            )
        
        # Register or update device token
        token_obj = device_token.register_or_update(
            db,
            user_id=current_user.user_id,
            device_token=device_data.device_token,
            device_type=device_data.device_type,
            device_id=device_data.device_id,
            app_version=device_data.app_version,
            os_version=device_data.os_version
        )
        
        logger.info(f"Device token registered for user {current_user.user_id}: {device_data.device_type}")
        
        return DeviceTokenResponse(
            success=True,
            message="Device token registered successfully",
            token_id=token_obj.token_id
        )
        
    except Exception as e:
        logger.error(f"Failed to register device token: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to register device token"
        )


@router.get("/devices", response_model=DeviceTokenListResponse)
async def get_my_devices(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> DeviceTokenListResponse:
    """Get all registered devices for current user"""
    try:
        devices = device_token.get_by_user(db, user_id=current_user.user_id)
        
        return DeviceTokenListResponse(
            success=True,
            devices=devices,
            total=len(devices)
        )
        
    except Exception as e:
        logger.error(f"Failed to get user devices: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve devices"
        )


@router.delete("/devices/{token_id}", response_model=DeviceTokenResponse)
async def deactivate_device_token(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    token_id: UUID
) -> DeviceTokenResponse:
    """Deactivate a specific device token"""
    try:
        # Get the token and verify ownership
        token_obj = device_token.get(db, id=token_id)
        if not token_obj:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Device token not found"
            )
        
        if token_obj.user_id != current_user.user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to deactivate this device token"
            )
        
        # Deactivate the token
        success = device_token.deactivate_token(db, token_id=token_id)
        
        if success:
            logger.info(f"Device token deactivated: {token_id}")
            return DeviceTokenResponse(
                success=True,
                message="Device token deactivated successfully",
                token_id=token_id
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to deactivate device token"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to deactivate device token: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to deactivate device token"
        )


@router.delete("/devices", response_model=DeviceTokenResponse)
async def deactivate_all_devices(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> DeviceTokenResponse:
    """Deactivate all device tokens for current user"""
    try:
        count = device_token.deactivate_user_tokens(db, user_id=current_user.user_id)
        
        logger.info(f"Deactivated {count} device tokens for user {current_user.user_id}")
        
        return DeviceTokenResponse(
            success=True,
            message=f"Deactivated {count} device tokens successfully"
        )
        
    except Exception as e:
        logger.error(f"Failed to deactivate user devices: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to deactivate devices"
        )


@router.post("/test", response_model=DeviceTokenResponse)
async def send_test_notification(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    test_data: NotificationTestRequest
) -> DeviceTokenResponse:
    """
    Send a test notification to user's devices
    
    Useful for testing push notification setup
    """
    try:
        if test_data.device_token_id:
            # Send to specific device
            token_obj = device_token.get(db, id=test_data.device_token_id)
            if not token_obj or token_obj.user_id != current_user.user_id:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Device token not found"
                )
            
            if not token_obj.is_active:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Device token is not active"
                )
            
            # Send test notification
            from app.services.firebase_service import firebase_service
            
            payload = NotificationPayload(
                title=test_data.title,
                body=test_data.body,
                data={"test": "true", "user_id": str(current_user.user_id)}
            )
            
            result = await firebase_service.send_notification(
                token_obj.device_token,
                payload
            )
            
            if result.success:
                # Update last used
                device_token.update_last_used(db, token_id=token_obj.token_id)
                
                return DeviceTokenResponse(
                    success=True,
                    message="Test notification sent successfully",
                    token_id=token_obj.token_id
                )
            else:
                return DeviceTokenResponse(
                    success=False,
                    message=f"Failed to send test notification: {result.error}"
                )
        else:
            # Send to all active devices
            user_devices = device_token.get_active_by_user(db, user_id=current_user.user_id)
            
            if not user_devices:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="No active devices found"
                )
            
            # Send to all devices
            success_count = 0
            for device in user_devices:
                from app.services.firebase_service import firebase_service
                
                payload = NotificationPayload(
                    title=test_data.title,
                    body=test_data.body,
                    data={"test": "true", "user_id": str(current_user.user_id)}
                )
                
                result = await firebase_service.send_notification(
                    device.device_token,
                    payload
                )
                
                if result.success:
                    success_count += 1
                    device_token.update_last_used(db, token_id=device.token_id)
            
            return DeviceTokenResponse(
                success=success_count > 0,
                message=f"Test notification sent to {success_count}/{len(user_devices)} devices"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to send test notification: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send test notification"
        )


# Admin endpoints for template management
@router.get("/admin/templates", response_model=NotificationTemplateListResponse)
async def get_notification_templates(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    category: Optional[str] = None,
    notification_type: Optional[str] = None,
    is_active: Optional[bool] = None,
    search: Optional[str] = None
) -> NotificationTemplateListResponse:
    """
    Get notification templates (Admin only)

    Filter templates by category, type, status, or search term
    """
    # TODO: Add admin role check
    # if not current_user.is_admin:
    #     raise HTTPException(status_code=403, detail="Admin access required")

    try:
        templates = notification_template.search_templates(
            db,
            search_term=search,
            category=category,
            notification_type=notification_type,
            is_active=is_active
        )

        return NotificationTemplateListResponse(
            success=True,
            templates=templates,
            total=len(templates)
        )

    except Exception as e:
        logger.error(f"Failed to get templates: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve templates"
        )


@router.post("/admin/templates", response_model=NotificationTemplateResponse)
async def create_notification_template(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    template_data: NotificationTemplateCreate
) -> NotificationTemplateResponse:
    """Create new notification template (Admin only)"""
    # TODO: Add admin role check

    try:
        template = notification_template.create_template(db, obj_in=template_data)

        logger.info(f"Template created: {template.template_key}")

        return NotificationTemplateResponse(
            success=True,
            message="Template created successfully",
            template_id=template.template_id
        )

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to create template: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create template"
        )


@router.put("/admin/templates/{template_id}", response_model=NotificationTemplateResponse)
async def update_notification_template(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    template_id: UUID,
    template_data: NotificationTemplateUpdate
) -> NotificationTemplateResponse:
    """Update notification template (Admin only)"""
    # TODO: Add admin role check

    try:
        template = notification_template.update_template(
            db, template_id=template_id, obj_in=template_data
        )

        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Template not found"
            )

        logger.info(f"Template updated: {template.template_key}")

        return NotificationTemplateResponse(
            success=True,
            message="Template updated successfully",
            template_id=template.template_id
        )

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update template: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update template"
        )


@router.post("/admin/send", response_model=SendNotificationResponse)
async def send_notification_by_template(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    notification_data: SendNotificationRequest
) -> SendNotificationResponse:
    """Send notification using template (Admin only)"""
    # TODO: Add admin role check

    try:
        if len(notification_data.user_ids) > 100:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Maximum 100 users per request"
            )

        result = await notification_service.send_batch_notification(
            db,
            user_ids=notification_data.user_ids,
            template_key=notification_data.template_key,
            variables=notification_data.variables,
            language=notification_data.language
        )

        return SendNotificationResponse(
            success=result["success"],
            message="Notifications processed",
            sent_count=result["success_users"],
            total_count=result["total_users"],
            results=result["results"]
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to send notifications: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send notifications"
        )


@router.post("/admin/templates/seed", response_model=NotificationTemplateResponse)
async def seed_default_templates(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> NotificationTemplateResponse:
    """Seed database with default notification templates (Admin only)"""
    # TODO: Add admin role check

    try:
        created_count = 0

        for template_data in TEMPLATE_EXAMPLES.values():
            # Check if template already exists
            existing = notification_template.get_by_key(
                db, template_key=template_data["template_key"]
            )

            if not existing:
                notification_template.create_template(
                    db, obj_in=NotificationTemplateCreate(**template_data)
                )
                created_count += 1

        logger.info(f"Seeded {created_count} default templates")

        return NotificationTemplateResponse(
            success=True,
            message=f"Created {created_count} default templates"
        )

    except Exception as e:
        logger.error(f"Failed to seed templates: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to seed templates"
        )


# Analytics endpoints
@router.get("/admin/analytics/overview", response_model=NotificationAnalyticsResponse)
async def get_notification_analytics(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    days: Optional[int] = 30
) -> NotificationAnalyticsResponse:
    """Get notification analytics overview (Admin only)"""
    # TODO: Add admin role check

    try:
        from datetime import datetime, timedelta

        start_date = datetime.utcnow() - timedelta(days=days) if days else None

        analytics = notification_log.get_delivery_stats(
            db, start_date=start_date
        )

        return NotificationAnalyticsResponse(
            success=True,
            analytics=analytics
        )

    except Exception as e:
        logger.error(f"Failed to get analytics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve analytics"
        )


@router.get("/admin/analytics/templates", response_model=TemplatePerformanceResponse)
async def get_template_performance(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    days: Optional[int] = 30
) -> TemplatePerformanceResponse:
    """Get template performance metrics (Admin only)"""
    # TODO: Add admin role check

    try:
        from datetime import datetime, timedelta

        start_date = datetime.utcnow() - timedelta(days=days) if days else None

        templates = notification_log.get_template_performance(
            db, start_date=start_date
        )

        return TemplatePerformanceResponse(
            success=True,
            templates=templates
        )

    except Exception as e:
        logger.error(f"Failed to get template performance: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve template performance"
        )


@router.get("/admin/analytics/daily", response_model=DailyStatsResponse)
async def get_daily_stats(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    days: Optional[int] = 30
) -> DailyStatsResponse:
    """Get daily notification statistics (Admin only)"""
    # TODO: Add admin role check

    try:
        stats = notification_log.get_daily_stats(db, days=days)

        return DailyStatsResponse(
            success=True,
            stats=stats
        )

    except Exception as e:
        logger.error(f"Failed to get daily stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve daily statistics"
        )


@router.get("/my-engagement", response_model=UserEngagementResponse)
async def get_my_engagement(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    days: Optional[int] = 30
) -> UserEngagementResponse:
    """Get current user's notification engagement metrics"""
    try:
        engagement = notification_log.get_user_engagement(
            db, user_id=current_user.user_id, days=days
        )

        return UserEngagementResponse(
            success=True,
            engagement=engagement
        )

    except Exception as e:
        logger.error(f"Failed to get user engagement: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve engagement metrics"
        )


@router.post("/click", response_model=NotificationClickResponse)
async def track_notification_click(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    click_data: NotificationClickRequest
) -> NotificationClickResponse:
    """Track notification click"""
    try:
        # Verify the notification belongs to the current user
        log = notification_log.get(db, id=click_data.log_id)
        if not log:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Notification not found"
            )

        if log.user_id != current_user.user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to track this notification"
            )

        # Mark as clicked
        success = notification_log.mark_as_clicked(db, log_id=click_data.log_id)

        if success:
            return NotificationClickResponse(
                success=True,
                message="Click tracked successfully"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to track click"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to track click: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to track notification click"
        )
