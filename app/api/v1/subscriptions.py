from typing import List
from decimal import Decimal
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.api.deps import get_db, get_current_user
from app.models.user import User
from app.crud.crud_referral import referral_voucher
from app.schemas.referral import (
    SubscriptionOffering,
    PurchaseOfferingsResponse,
    WebhookPaymentData
)
from app.services.referral_service import referral_service

router = APIRouter()


@router.get("/purchase-offerings", response_model=PurchaseOfferingsResponse)
def get_purchase_offerings(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get subscription offerings with applicable discounts.
    
    This is the main endpoint that client calls before showing purchase screen.
    It calculates all available discounts according to business rules:
    
    - Rule B2 & A2: New user discount (5$ if referred and first purchase)
    - Rule A1 & A2: Referral voucher discount (5$ if available vouchers exist)
    - Rule A2: Both discounts can be combined for first purchase (max 10$)
    """
    total_discount = Decimal("0.00")
    applied_vouchers = []
    new_user_discount_applied = False
    
    # Check for new user discount (Rule B2 & A2)
    if not current_user.has_made_first_purchase and current_user.referred_by_user_id:
        total_discount += Decimal("5.00")
        new_user_discount_applied = True
    
    # Check for referral voucher discount (Rule A1 & A2)
    available_voucher = referral_voucher.get_first_available_voucher(db, current_user.user_id)
    if available_voucher:
        total_discount += available_voucher.amount_usd
        applied_vouchers.append(available_voucher.voucher_id)
    
    # Define subscription packages
    base_offerings = [
        {
            "package_id": "weekly",
            "package_name": "Weekly Premium",
            "base_price_usd": Decimal("5.00")
        },
        {
            "package_id": "monthly", 
            "package_name": "Monthly Premium",
            "base_price_usd": Decimal("15.00")
        },
        {
            "package_id": "yearly",
            "package_name": "Yearly Premium", 
            "base_price_usd": Decimal("50.00")
        }
    ]
    
    # Apply discounts to offerings
    offerings = []
    for offering in base_offerings:
        discount_amount = min(total_discount, offering["base_price_usd"])
        final_price = max(Decimal("0.00"), offering["base_price_usd"] - discount_amount)
        
        offerings.append(SubscriptionOffering(
            package_id=offering["package_id"],
            package_name=offering["package_name"],
            base_price_usd=offering["base_price_usd"],
            final_price_usd=final_price,
            discount_amount_usd=discount_amount
        ))
    
    return PurchaseOfferingsResponse(
        offerings=offerings,
        applied_vouchers=applied_vouchers,
        new_user_discount_applied=new_user_discount_applied,
        total_discount_usd=total_discount
    )


@router.post("/webhook/payment-success")
def handle_payment_success_webhook(
    payment_data: WebhookPaymentData,
    db: Session = Depends(get_db)
):
    """
    Webhook endpoint to handle successful payments from Google Play/App Store.
    
    This endpoint processes the payment and handles all referral logic:
    - Marks user as having made first purchase (Rule A3)
    - Marks applied vouchers as used
    - Rewards referrer with new voucher if applicable
    - Creates referral history records
    """
    try:
        # Validate webhook (in production, verify signature from payment provider)
        # TODO: Add webhook signature validation
        
        result = referral_service.process_first_purchase(
            db=db,
            user_id=payment_data.user_id,
            applied_vouchers=payment_data.applied_vouchers
        )
        
        if not result["success"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
        
        return {
            "status": "success",
            "message": result["message"],
            "referrer_rewarded": result.get("referrer_rewarded", False)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Webhook processing failed: {str(e)}"
        )
