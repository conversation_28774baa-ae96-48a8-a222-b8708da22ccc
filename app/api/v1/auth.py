from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.schemas.auth import GoogleLoginRequest, AppleLoginRequest, LoginResponse, RefreshTokenRequest, RefreshTokenResponse
from app.services.auth import AuthService


router = APIRouter()


@router.post("/login/google", response_model=LoginResponse)
async def login_with_google(
    request: GoogleLoginRequest,
    db: Session = Depends(get_db)
):
    """
    Login with Google ID token
    """
    # Verify Google token
    user_info = await AuthService.verify_google_token(request.token)
    if not user_info:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid Google token"
        )

    # Check if email is verified
    if not user_info.get('email_verified', False):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Email not verified with Google"
        )

    # Authenticate or create user
    user = AuthService.authenticate_or_create_user(
        db=db,
        user_info=user_info,
        auth_provider="google"
    )

    # Create tokens
    tokens = AuthService.create_user_tokens(user)

    # Convert user to dict for response
    user_dict = {
        "user_id": str(user.user_id),
        "email": user.email,
        "display_name": user.display_name,
        "photo_url": user.photo_url,
        "auth_provider": user.auth_provider,
        "referral_code": user.referral_code,
        "created_at": user.created_at.isoformat() if user.created_at else None,
        "last_login_at": user.last_login_at.isoformat() if user.last_login_at else None
    }

    return LoginResponse(
        access_token=tokens["access_token"],
        refresh_token=tokens["refresh_token"],
        token_type=tokens["token_type"],
        user=user_dict
    )


@router.post("/login/apple", response_model=LoginResponse)
async def login_with_apple(
    request: AppleLoginRequest,
    db: Session = Depends(get_db)
):
    """
    Login with Apple ID token
    """
    # Verify Apple token
    user_info = await AuthService.verify_apple_token(request.token)
    if not user_info:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid Apple token"
        )

    # Authenticate or create user
    user = AuthService.authenticate_or_create_user(
        db=db,
        user_info=user_info,
        auth_provider="apple"
    )

    # Create tokens
    tokens = AuthService.create_user_tokens(user)

    # Convert user to dict for response
    user_dict = {
        "user_id": str(user.user_id),
        "email": user.email,
        "display_name": user.display_name,
        "photo_url": user.photo_url,
        "auth_provider": user.auth_provider,
        "referral_code": user.referral_code,
        "created_at": user.created_at.isoformat() if user.created_at else None,
        "last_login_at": user.last_login_at.isoformat() if user.last_login_at else None
    }

    return LoginResponse(
        access_token=tokens["access_token"],
        refresh_token=tokens["refresh_token"],
        token_type=tokens["token_type"],
        user=user_dict
    )


@router.post("/refresh", response_model=RefreshTokenResponse)
async def refresh_token(
    request: RefreshTokenRequest
):
    """
    Refresh access token using refresh token
    """
    # Refresh the access token
    tokens = AuthService.refresh_access_token(request.refresh_token)
    if not tokens:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired refresh token"
        )

    return RefreshTokenResponse(
        access_token=tokens["access_token"],
        token_type=tokens["token_type"]
    )
