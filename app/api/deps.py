# Common API dependencies

from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.core.security import verify_token, verify_token_flexible, is_firebase_token
from app.crud.user import get_user_by_id, get_user_by_provider, get_user_by_provider_user_id
from app.models.user import User


# Security scheme
security = HTTPBearer()


# Database dependency - Use get_db directly from session
# def get_database():
#     """Get database session"""
#     return get_db()


# Authentication dependencies
async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """Get current authenticated user (supports both JWT and Firebase tokens)"""
    token = credentials.credentials

    # First try JWT token verification
    user_id = verify_token(token)

    if user_id is None:
        # Try Firebase token verification
        user_id = await verify_token_flexible(token)

        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # For Firebase tokens, user_id is Firebase UID
        # We need to find user by Firebase UID (provider_user_id)
        if is_firebase_token(token):
            user = get_user_by_provider_user_id(db, user_id)
        else:
            user = get_user_by_id(db, user_id)
    else:
        # For JWT tokens, user_id is our internal user_id
        user = get_user_by_id(db, user_id)

    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return user


def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """Get current active user"""
    # Add any additional checks for active users here if needed
    return current_user


def get_current_superuser(
    current_user: User = Depends(get_current_user)
) -> User:
    """Get current superuser"""
    # Add superuser check logic here if needed
    # For now, just return the current user
    return current_user


async def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: Session = Depends(get_db)
) -> Optional[User]:
    """Get current user if authenticated, None otherwise (supports both JWT and Firebase tokens)"""
    if not credentials:
        return None

    token = credentials.credentials

    # First try JWT token verification
    user_id = verify_token(token)

    if user_id is None:
        # Try Firebase token verification
        user_id = await verify_token_flexible(token)
        if user_id is None:
            return None

        # For Firebase tokens, find user by Firebase UID
        if is_firebase_token(token):
            user = get_user_by_provider_user_id(db, user_id)
        else:
            user = get_user_by_id(db, user_id)
    else:
        # For JWT tokens, user_id is our internal user_id
        user = get_user_by_id(db, user_id)

    return user


# Pagination dependencies
def get_pagination_params(
    skip: int = 0,
    limit: int = 100
) -> dict:
    """Get pagination parameters"""
    return {"skip": skip, "limit": min(limit, 100)}
