from typing import Optional, Dict, Any
import requests
from google.auth.transport import requests as google_requests
from google.oauth2 import id_token
from sqlalchemy.orm import Session

from app.crud.user import get_user_by_provider, create_user, update_last_login
from app.schemas.user import UserCreate
from app.models.user import User
from app.core.security import create_access_token, create_refresh_token, verify_refresh_token
from app.services.referral_service import referral_service
from app.services.firebase_auth_service import firebase_auth_service
from app.core.config import settings


class AuthService:

    @staticmethod
    async def verify_firebase_token(token: str) -> Optional[Dict[str, Any]]:
        """Verify Firebase ID token and return user info"""
        return await firebase_auth_service.verify_firebase_token(token)

    @staticmethod
    async def verify_google_token(token: str) -> Optional[Dict[str, Any]]:
        """Verify Google ID token and return user info"""
        try:
            # Verify the token with Google
            # Include audience validation if GOOG<PERSON>_CLIENT_ID is configured
            if settings.GOOGLE_CLIENT_ID:
                idinfo = id_token.verify_oauth2_token(
                    token, google_requests.Request(), audience=settings.GOOGLE_CLIENT_ID
                )
            else:
                idinfo = id_token.verify_oauth2_token(
                    token, google_requests.Request()
                )

            # Check if token is from Google
            if idinfo['iss'] not in ['accounts.google.com', 'https://accounts.google.com']:
                return None

            return {
                'email': idinfo.get('email'),
                'name': idinfo.get('name'),
                'picture': idinfo.get('picture'),
                'provider_user_id': idinfo.get('sub'),
                'email_verified': idinfo.get('email_verified', False)
            }
        except ValueError:
            # Invalid token
            return None
    
    @staticmethod
    async def verify_apple_token(token: str) -> Optional[Dict[str, Any]]:
        """Verify Apple ID token and return user info"""
        try:
            # For Apple, we need to verify the JWT token
            # This is a simplified version - in production, you should verify with Apple's public keys
            import jwt
            
            # Decode without verification for now (you should implement proper verification)
            # In production, fetch Apple's public keys and verify the signature
            decoded = jwt.decode(token, options={"verify_signature": False})
            
            return {
                'email': decoded.get('email'),
                'name': decoded.get('name'),
                'provider_user_id': decoded.get('sub'),
                'email_verified': decoded.get('email_verified', False)
            }
        except Exception:
            # Invalid token
            return None
    
    @staticmethod
    def authenticate_or_create_user(
        db: Session, 
        user_info: Dict[str, Any], 
        auth_provider: str
    ) -> User:
        """Find existing user or create new one"""
        
        # Try to find existing user
        existing_user = get_user_by_provider(
            db=db,
            email=user_info['email'],
            auth_provider=auth_provider,
            provider_user_id=user_info.get('provider_user_id')
        )
        
        if existing_user:
            # Update last login
            update_last_login(db, existing_user.user_id)
            return existing_user
        
        # Create new user
        user_create = UserCreate(
            email=user_info['email'],
            display_name=user_info.get('name'),
            photo_url=user_info.get('picture'),
            auth_provider=auth_provider,
            provider_user_id=user_info.get('provider_user_id')
        )

        new_user = create_user(db, user_create)

        # Generate referral code for new user
        try:
            referral_code = referral_service.generate_referral_code(
                db,
                new_user.display_name or new_user.email.split('@')[0]
            )
            new_user.referral_code = referral_code
            db.add(new_user)
            db.commit()
            db.refresh(new_user)
        except Exception as e:
            # Log error but don't fail user creation
            print(f"Warning: Could not generate referral code for user {new_user.email}: {e}")

        # Trigger welcome notification for new user
        try:
            from app.tasks.notification_tasks import trigger_welcome_notification
            trigger_welcome_notification(new_user.user_id)
        except Exception as e:
            # Don't fail user creation if notification fails
            print(f"Warning: Could not send welcome notification to user {new_user.email}: {e}")

        return new_user
    
    @staticmethod
    def create_user_tokens(user: User) -> Dict[str, str]:
        """Create access and refresh tokens for user"""
        access_token = create_access_token(subject=str(user.user_id))
        refresh_token = create_refresh_token(subject=str(user.user_id))

        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer"
        }

    @staticmethod
    def refresh_access_token(refresh_token: str) -> Optional[Dict[str, str]]:
        """Verify refresh token and create new access token"""
        # Verify the refresh token
        user_id = verify_refresh_token(refresh_token)
        if not user_id:
            return None

        # Create new access token
        access_token = create_access_token(subject=user_id)

        return {
            "access_token": access_token,
            "token_type": "bearer"
        }
