"""
Firebase Authentication service for verifying Firebase ID tokens.
"""

import logging
from typing import Optional, Dict, Any
import firebase_admin
from firebase_admin import credentials, auth
from app.core.config import settings

logger = logging.getLogger(__name__)


class FirebaseAuthService:
    """Firebase Authentication service for token verification"""
    
    def __init__(self):
        self._app = None
        self._initialize_firebase()
    
    def _initialize_firebase(self):
        """Initialize Firebase Admin SDK if not already initialized"""
        try:
            if not firebase_admin._apps:
                # Initialize Firebase app if not already done
                if settings.FIREBASE_CREDENTIALS_PATH:
                    cred = credentials.Certificate(settings.FIREBASE_CREDENTIALS_PATH)
                    self._app = firebase_admin.initialize_app(cred, {
                        'projectId': settings.FIREBASE_PROJECT_ID
                    })
                else:
                    # Use default credentials (for production with service account)
                    self._app = firebase_admin.initialize_app()
                
                logger.info("Firebase Admin SDK initialized successfully for authentication")
            else:
                self._app = firebase_admin.get_app()
                logger.info("Using existing Firebase app instance for authentication")
                
        except Exception as e:
            logger.error(f"Failed to initialize Firebase for authentication: {e}")
            raise
    
    async def verify_firebase_token(self, id_token: str) -> Optional[Dict[str, Any]]:
        """
        Verify Firebase ID token and return user information
        
        Args:
            id_token: Firebase ID token from client
            
        Returns:
            Dict containing user information if token is valid, None otherwise
        """
        try:
            # Verify the ID token
            decoded_token = auth.verify_id_token(id_token)
            
            # Extract user information
            user_info = {
                'uid': decoded_token.get('uid'),
                'email': decoded_token.get('email'),
                'email_verified': decoded_token.get('email_verified', False),
                'name': decoded_token.get('name'),
                'picture': decoded_token.get('picture'),
                'provider_user_id': decoded_token.get('uid'),  # Firebase UID
                'firebase_claims': decoded_token  # Store full claims for reference
            }
            
            # Determine auth provider from Firebase claims
            firebase_info = decoded_token.get('firebase', {})
            sign_in_provider = firebase_info.get('sign_in_provider', 'firebase')
            
            # Map Firebase providers to our system
            provider_mapping = {
                'google.com': 'google',
                'apple.com': 'apple',
                'facebook.com': 'facebook',
                'password': 'firebase',
                'anonymous': 'anonymous'
            }
            
            user_info['auth_provider'] = provider_mapping.get(sign_in_provider, 'firebase')
            
            logger.info(f"Successfully verified Firebase token for user: {user_info['email']}")
            return user_info
            
        except auth.InvalidIdTokenError as e:
            logger.warning(f"Invalid Firebase ID token: {e}")
            return None
        except auth.ExpiredIdTokenError as e:
            logger.warning(f"Expired Firebase ID token: {e}")
            return None
        except auth.RevokedIdTokenError as e:
            logger.warning(f"Revoked Firebase ID token: {e}")
            return None
        except Exception as e:
            logger.error(f"Error verifying Firebase token: {e}")
            return None
    
    async def get_user_by_uid(self, uid: str) -> Optional[Dict[str, Any]]:
        """
        Get user information from Firebase by UID
        
        Args:
            uid: Firebase user UID
            
        Returns:
            Dict containing user information if found, None otherwise
        """
        try:
            user_record = auth.get_user(uid)
            
            user_info = {
                'uid': user_record.uid,
                'email': user_record.email,
                'email_verified': user_record.email_verified,
                'display_name': user_record.display_name,
                'photo_url': user_record.photo_url,
                'disabled': user_record.disabled,
                'provider_data': [
                    {
                        'provider_id': provider.provider_id,
                        'uid': provider.uid,
                        'email': provider.email,
                        'display_name': provider.display_name,
                        'photo_url': provider.photo_url
                    }
                    for provider in user_record.provider_data
                ]
            }
            
            return user_info
            
        except auth.UserNotFoundError:
            logger.warning(f"Firebase user not found: {uid}")
            return None
        except Exception as e:
            logger.error(f"Error getting Firebase user: {e}")
            return None
    
    async def revoke_refresh_tokens(self, uid: str) -> bool:
        """
        Revoke all refresh tokens for a user
        
        Args:
            uid: Firebase user UID
            
        Returns:
            True if successful, False otherwise
        """
        try:
            auth.revoke_refresh_tokens(uid)
            logger.info(f"Successfully revoked refresh tokens for user: {uid}")
            return True
        except Exception as e:
            logger.error(f"Error revoking refresh tokens: {e}")
            return False
    
    async def verify_session_cookie(self, session_cookie: str) -> Optional[Dict[str, Any]]:
        """
        Verify Firebase session cookie (if using session cookies)
        
        Args:
            session_cookie: Firebase session cookie
            
        Returns:
            Dict containing user information if valid, None otherwise
        """
        try:
            decoded_claims = auth.verify_session_cookie(session_cookie)
            return decoded_claims
        except Exception as e:
            logger.error(f"Error verifying session cookie: {e}")
            return None


# Global Firebase Auth service instance
firebase_auth_service = FirebaseAuthService()
