"""
Firebase Cloud Messaging (FCM) service for sending push notifications.
"""

import logging
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass
import firebase_admin
from firebase_admin import credentials, messaging
from app.core.config import settings

logger = logging.getLogger(__name__)


@dataclass
class NotificationPayload:
    """Structured notification payload"""
    title: str
    body: str
    image_url: Optional[str] = None
    action_url: Optional[str] = None
    data: Optional[Dict[str, str]] = None
    priority: str = "normal"  # 'high', 'normal', 'low'
    sound: Optional[str] = "default"


@dataclass
class FCMResponse:
    """FCM response wrapper"""
    success: bool
    message_id: Optional[str] = None
    error: Optional[str] = None
    failure_count: int = 0
    success_count: int = 0


class FirebaseService:
    """Firebase Cloud Messaging service"""
    
    def __init__(self):
        self._app = None
        self._initialize_firebase()
    
    def _initialize_firebase(self):
        """Initialize Firebase Admin SDK"""
        try:
            if not firebase_admin._apps:
                # Initialize Firebase app if not already done
                if settings.FIREBASE_CREDENTIALS_PATH:
                    cred = credentials.Certificate(settings.FIREBASE_CREDENTIALS_PATH)
                    self._app = firebase_admin.initialize_app(cred, {
                        'projectId': settings.FIREBASE_PROJECT_ID
                    })
                else:
                    # Use default credentials (for production with service account)
                    self._app = firebase_admin.initialize_app()
                
                logger.info("Firebase Admin SDK initialized successfully")
            else:
                self._app = firebase_admin.get_app()
                logger.info("Using existing Firebase app instance")
                
        except Exception as e:
            logger.error(f"Failed to initialize Firebase: {e}")
            raise
    
    async def send_notification(
        self,
        device_token: str,
        payload: NotificationPayload
    ) -> FCMResponse:
        """
        Send notification to a single device
        
        Args:
            device_token: FCM device token
            payload: Notification payload
            
        Returns:
            FCMResponse with result
        """
        try:
            # Build FCM message
            message = self._build_message(device_token, payload)
            
            # Send message
            response = messaging.send(message)
            
            logger.info(f"Successfully sent notification to {device_token[:20]}...")
            return FCMResponse(
                success=True,
                message_id=response,
                success_count=1
            )
            
        except messaging.UnregisteredError:
            logger.warning(f"Device token is unregistered: {device_token[:20]}...")
            return FCMResponse(
                success=False,
                error="unregistered_token",
                failure_count=1
            )
        except messaging.InvalidArgumentError as e:
            logger.error(f"Invalid argument for FCM: {e}")
            return FCMResponse(
                success=False,
                error=f"invalid_argument: {str(e)}",
                failure_count=1
            )
        except Exception as e:
            logger.error(f"Failed to send notification: {e}")
            return FCMResponse(
                success=False,
                error=str(e),
                failure_count=1
            )
    
    async def send_batch_notifications(
        self,
        device_tokens: List[str],
        payload: NotificationPayload
    ) -> FCMResponse:
        """
        Send notifications to multiple devices (batch)
        
        Args:
            device_tokens: List of FCM device tokens
            payload: Notification payload
            
        Returns:
            FCMResponse with batch results
        """
        if not device_tokens:
            return FCMResponse(success=True, success_count=0)
        
        try:
            # Build messages for batch
            messages = [
                self._build_message(token, payload)
                for token in device_tokens
            ]
            
            # Send batch
            response = messaging.send_all(messages)
            
            # Process results
            failed_tokens = []
            for idx, result in enumerate(response.responses):
                if not result.success:
                    failed_tokens.append({
                        'token': device_tokens[idx],
                        'error': result.exception
                    })
                    logger.warning(f"Failed to send to {device_tokens[idx][:20]}...: {result.exception}")
            
            logger.info(f"Batch notification sent: {response.success_count} success, {response.failure_count} failed")
            
            return FCMResponse(
                success=response.failure_count == 0,
                success_count=response.success_count,
                failure_count=response.failure_count,
                error=f"Failed tokens: {len(failed_tokens)}" if failed_tokens else None
            )
            
        except Exception as e:
            logger.error(f"Failed to send batch notifications: {e}")
            return FCMResponse(
                success=False,
                error=str(e),
                failure_count=len(device_tokens)
            )
    
    def _build_message(self, device_token: str, payload: NotificationPayload) -> messaging.Message:
        """Build FCM message from payload"""
        
        # Build notification
        notification = messaging.Notification(
            title=payload.title,
            body=payload.body,
            image=payload.image_url
        )
        
        # Build data payload
        data = payload.data or {}
        if payload.action_url:
            data['action_url'] = payload.action_url
        
        # Build Android config
        android_config = messaging.AndroidConfig(
            priority=payload.priority,
            notification=messaging.AndroidNotification(
                sound=payload.sound,
                click_action=payload.action_url
            )
        )
        
        # Build iOS config
        apns_config = messaging.APNSConfig(
            payload=messaging.APNSPayload(
                aps=messaging.Aps(
                    sound=payload.sound,
                    badge=1
                )
            )
        )
        
        return messaging.Message(
            token=device_token,
            notification=notification,
            data=data,
            android=android_config,
            apns=apns_config
        )
    
    async def validate_token(self, device_token: str) -> bool:
        """Validate if device token is still valid"""
        try:
            # Try to send a test message (dry run)
            message = messaging.Message(
                token=device_token,
                notification=messaging.Notification(title="Test", body="Test")
            )
            
            # Dry run to validate token
            messaging.send(message, dry_run=True)
            return True
            
        except messaging.UnregisteredError:
            return False
        except Exception:
            return False


# Global Firebase service instance
firebase_service = FirebaseService()
