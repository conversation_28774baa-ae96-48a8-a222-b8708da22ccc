"""
Main notification service for handling template rendering and notification delivery.
"""

import logging
from typing import List, Dict, Any, Optional, Union
from uuid import UUID
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from jinja2 import Template

from app.core.config import settings
from app.models.user import User
from app.models.device_token import DeviceToken
from app.models.notification_template import NotificationTemplate
from app.models.notification_log import NotificationLog
from app.services.firebase_service import firebase_service, NotificationPayload
from app.crud.crud_user import user as user_crud

logger = logging.getLogger(__name__)


class NotificationService:
    """Main notification service"""
    
    def __init__(self):
        self.firebase = firebase_service
        self.default_language = settings.NOTIFICATION_DEFAULT_LANGUAGE
    
    async def send_notification_by_template(
        self,
        db: Session,
        user_id: UUID,
        template_key: str,
        variables: Optional[Dict[str, Any]] = None,
        language: Optional[str] = None,
        scheduled_at: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        Send notification using template
        
        Args:
            db: Database session
            user_id: Target user ID
            template_key: Template identifier
            variables: Template variables for rendering
            language: Preferred language (defaults to user preference or 'vi')
            scheduled_at: Schedule notification for later (optional)
            
        Returns:
            Result dictionary with success status and details
        """
        try:
            # Get user and their devices
            user = user_crud.get(db, id=user_id)
            if not user:
                return {"success": False, "error": "User not found"}
            
            # Get active device tokens
            device_tokens = db.query(DeviceToken).filter(
                DeviceToken.user_id == user_id,
                DeviceToken.is_active == True
            ).all()
            
            if not device_tokens:
                logger.warning(f"No active device tokens for user {user_id}")
                return {"success": False, "error": "No active devices"}
            
            # Get notification template
            template = db.query(NotificationTemplate).filter(
                NotificationTemplate.template_key == template_key,
                NotificationTemplate.is_active == True
            ).first()
            
            if not template:
                return {"success": False, "error": f"Template '{template_key}' not found"}
            
            # Determine language
            target_language = language or self.default_language
            
            # Render template
            rendered_content = self._render_template(template, variables or {}, target_language)
            
            # Create notification payload
            payload = NotificationPayload(
                title=rendered_content["title"],
                body=rendered_content["body"],
                image_url=template.image_url,
                action_url=template.action_url,
                data=variables or {},
                priority=template.priority,
                sound=template.sound
            )
            
            # Send to all user devices
            results = []
            for device_token in device_tokens:
                # Create notification log entry
                notification_log = NotificationLog(
                    user_id=user_id,
                    device_token_id=device_token.token_id,
                    template_id=template.template_id,
                    title=payload.title,
                    body=payload.body,
                    image_url=payload.image_url,
                    action_url=payload.action_url,
                    notification_type=template.notification_type,
                    category=template.category,
                    priority=payload.priority,
                    language=target_language,
                    template_variables=variables,
                    scheduled_at=scheduled_at,
                    delivery_status="pending"
                )
                
                db.add(notification_log)
                db.flush()  # Get the log ID
                
                if scheduled_at and scheduled_at > datetime.utcnow():
                    # Schedule for later
                    notification_log.delivery_status = "scheduled"
                    results.append({
                        "device_token_id": device_token.token_id,
                        "status": "scheduled",
                        "log_id": notification_log.log_id
                    })
                else:
                    # Send immediately
                    fcm_result = await self.firebase.send_notification(
                        device_token.device_token,
                        payload
                    )
                    
                    # Update log with result
                    if fcm_result.success:
                        notification_log.delivery_status = "sent"
                        notification_log.firebase_message_id = fcm_result.message_id
                        notification_log.sent_at = datetime.utcnow()
                        
                        # Update device token last used
                        device_token.last_used_at = datetime.utcnow()
                    else:
                        notification_log.delivery_status = "failed"
                        notification_log.delivery_error = fcm_result.error
                        
                        # Handle unregistered tokens
                        if fcm_result.error == "unregistered_token":
                            device_token.is_active = False
                            logger.info(f"Deactivated unregistered token: {device_token.token_id}")
                    
                    results.append({
                        "device_token_id": device_token.token_id,
                        "status": "sent" if fcm_result.success else "failed",
                        "error": fcm_result.error,
                        "log_id": notification_log.log_id
                    })
            
            db.commit()
            
            success_count = sum(1 for r in results if r["status"] == "sent")
            total_count = len(results)
            
            return {
                "success": success_count > 0,
                "sent_count": success_count,
                "total_count": total_count,
                "results": results
            }
            
        except Exception as e:
            db.rollback()
            logger.error(f"Failed to send notification: {e}")
            return {"success": False, "error": str(e)}
    
    async def send_batch_notification(
        self,
        db: Session,
        user_ids: List[UUID],
        template_key: str,
        variables: Optional[Dict[str, Any]] = None,
        language: Optional[str] = None
    ) -> Dict[str, Any]:
        """Send notification to multiple users"""
        results = []
        
        for user_id in user_ids:
            result = await self.send_notification_by_template(
                db, user_id, template_key, variables, language
            )
            results.append({
                "user_id": user_id,
                "result": result
            })
        
        success_count = sum(1 for r in results if r["result"]["success"])
        
        return {
            "success": success_count > 0,
            "total_users": len(user_ids),
            "success_users": success_count,
            "results": results
        }
    
    def _render_template(
        self,
        template: NotificationTemplate,
        variables: Dict[str, Any],
        language: str
    ) -> Dict[str, str]:
        """Render notification template with variables"""
        try:
            # Get localized templates
            title_template = template.get_localized_title(language)
            body_template = template.get_localized_body(language)
            
            # Render with Jinja2
            title = Template(title_template).render(**variables)
            body = Template(body_template).render(**variables)
            
            return {
                "title": title,
                "body": body
            }
            
        except Exception as e:
            logger.error(f"Template rendering failed: {e}")
            # Fallback to raw template
            return {
                "title": template.get_localized_title(language),
                "body": template.get_localized_body(language)
            }
    
    async def send_welcome_notification(self, db: Session, user_id: UUID) -> Dict[str, Any]:
        """Send welcome notification to new user"""
        user = user_crud.get(db, id=user_id)
        if not user:
            return {"success": False, "error": "User not found"}
        
        variables = {
            "user_name": user.display_name or "bạn",
            "app_name": settings.APP_NAME
        }
        
        return await self.send_notification_by_template(
            db, user_id, "welcome_new_user", variables
        )
    
    async def send_referral_reward_notification(
        self,
        db: Session,
        user_id: UUID,
        referred_user_name: str,
        reward_amount: float = 5.0
    ) -> Dict[str, Any]:
        """Send referral reward notification"""
        variables = {
            "referred_user_name": referred_user_name,
            "reward_amount": reward_amount
        }
        
        return await self.send_notification_by_template(
            db, user_id, "referral_reward", variables
        )


# Global notification service instance
notification_service = NotificationService()
