# Application Settings
APP_NAME=Bana_Chef_Server
APP_VERSION=0.1.0
DEBUG=True
SECRET_KEY=your-super-secret-key-here-change-in-production

# Server Configuration
HOST=0.0.0.0
PORT=8000

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/banachef_db
DB_HOST=localhost
DB_PORT=5432
DB_USER=username
DB_PASSWORD=password
DB_NAME=banachef_db

# JWT Configuration
JWT_SECRET_KEY=your-jwt-secret-key-here
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Google AI Configuration
GOOGLE_API_KEY=your-google-gemini-api-key-here

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Google Cloud Storage Configuration
GOOGLE_CLOUD_PROJECT_ID=your-gcp-project-id
GOOGLE_CLOUD_STORAGE_BUCKET=your-storage-bucket-name
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account-key.json

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080

# File Upload Configuration
MAX_FILE_SIZE=********  # 10MB in bytes
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp

# Logging Configuration
LOG_LEVEL=INFO
