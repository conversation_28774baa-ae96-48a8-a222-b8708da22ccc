# Development dependencies
# These are additional packages needed for development, testing, and code quality

# Testing
pytest==7.4.4
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-xdist==3.3.1
pytest-mock==3.12.0
httpx==0.25.2  # For testing FastAPI endpoints

# Code Quality
black==23.12.1
isort==5.13.2
flake8==6.1.0
mypy==1.8.0
pre-commit==3.6.0

# Security
bandit==1.7.5
safety==2.3.5
pip-audit==2.6.1

# Documentation
mkdocs==1.5.3
mkdocs-material==9.4.14
mkdocs-swagger-ui-tag==0.6.8

# Development tools
ipython==8.18.1
ipdb==0.13.13
python-dotenv==1.0.0

# Database tools
alembic==1.13.1  # For database migrations

# Performance testing
locust==2.17.0

# API testing
tavern==2.4.1

# Load testing
artillery==1.7.9  # Note: This is a Node.js package, included for reference
