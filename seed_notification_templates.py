#!/usr/bin/env python3
"""
Script to seed notification templates into the database.
"""

import sys
import os

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.db.session import SessionLocal
from app.crud.crud_notification_template import notification_template
from app.schemas.notification_template import NotificationTemplateCreate

# Default notification templates
DEFAULT_TEMPLATES = [
    {
        "template_key": "welcome_new_user",
        "template_name": "Welcome New User",
        "description": "Welcome message for newly registered users",
        "category": "transactional",
        "notification_type": "welcome",
        "title_templates": {
            "vi": "Chào mừng đến với Bana Chef! 👋",
            "en": "Welcome to Bana Chef! 👋"
        },
        "body_templates": {
            "vi": "Xin chào {user_name}! <PERSON><PERSON><PERSON> bắt đầu hành trình nấu ăn tuyệt vời cùng chúng tôi.",
            "en": "Hello {user_name}! Let's start your amazing cooking journey with us."
        },
        "action_url": "banachef://onboarding",
        "action_button_text": {
            "vi": "Bắt đầu",
            "en": "Get Started"
        },
        "priority": "normal",
        "badge_count": True,
        "is_active": True
    },
    {
        "template_key": "referral_reward",
        "template_name": "Referral Reward",
        "description": "Notification when user receives referral reward",
        "category": "transactional",
        "notification_type": "referral_complete",
        "title_templates": {
            "vi": "Bạn vừa nhận được voucher ${reward_amount}! 🎉",
            "en": "You just earned a ${reward_amount} voucher! 🎉"
        },
        "body_templates": {
            "vi": "Cảm ơn bạn đã giới thiệu {referred_user_name}. Voucher ${reward_amount} đã được thêm vào tài khoản!",
            "en": "Thanks for referring {referred_user_name}. ${reward_amount} voucher has been added to your account!"
        },
        "action_url": "banachef://vouchers",
        "action_button_text": {
            "vi": "Xem voucher",
            "en": "View Vouchers"
        },
        "priority": "high",
        "badge_count": True,
        "is_active": True
    },
    {
        "template_key": "recipe_suggestion",
        "template_name": "Daily Recipe Suggestion",
        "description": "Daily personalized recipe suggestions",
        "category": "marketing",
        "notification_type": "recipe_suggestion",
        "title_templates": {
            "vi": "Công thức mới dành cho bạn! 👨‍🍳",
            "en": "New recipe just for you! 👨‍🍳"
        },
        "body_templates": {
            "vi": "Hôm nay thử làm {recipe_name} nhé! Phù hợp với khẩu vị của bạn.",
            "en": "Try making {recipe_name} today! Perfect for your taste preferences."
        },
        "action_url": "banachef://recipe/{recipe_id}",
        "action_button_text": {
            "vi": "Xem công thức",
            "en": "View Recipe"
        },
        "priority": "normal",
        "badge_count": False,
        "is_active": True
    },
    {
        "template_key": "weekly_summary",
        "template_name": "Weekly Cooking Summary",
        "description": "Weekly summary of user's cooking activities",
        "category": "marketing",
        "notification_type": "weekly_summary",
        "title_templates": {
            "vi": "Tổng kết tuần nấu ăn của bạn! 📊",
            "en": "Your weekly cooking summary! 📊"
        },
        "body_templates": {
            "vi": "Tuần này bạn đã thử {recipes_tried} công thức! Món {favorite_cuisine} vẫn là sở thích của bạn.",
            "en": "This week you tried {recipes_tried} recipes! {favorite_cuisine} cuisine is still your favorite."
        },
        "action_url": "banachef://profile/stats",
        "action_button_text": {
            "vi": "Xem chi tiết",
            "en": "View Details"
        },
        "priority": "normal",
        "badge_count": False,
        "is_active": True
    },
    {
        "template_key": "user_reactivation",
        "template_name": "User Reactivation",
        "description": "Reactivation message for inactive users",
        "category": "marketing",
        "notification_type": "reactivation",
        "title_templates": {
            "vi": "Chúng tôi nhớ bạn! 🥺",
            "en": "We miss you! 🥺"
        },
        "body_templates": {
            "vi": "Đã {days_away} ngày bạn không ghé thăm Bana Chef. Có nhiều công thức mới đang chờ bạn khám phá!",
            "en": "It's been {days_away} days since you visited Bana Chef. There are many new recipes waiting for you to explore!"
        },
        "action_url": "banachef://home",
        "action_button_text": {
            "vi": "Khám phá ngay",
            "en": "Explore Now"
        },
        "priority": "normal",
        "badge_count": True,
        "is_active": True
    },
    {
        "template_key": "cooking_reminder",
        "template_name": "Cooking Reminder",
        "description": "Reminder to cook saved recipes",
        "category": "marketing",
        "notification_type": "cooking_reminder",
        "title_templates": {
            "vi": "Đã đến giờ nấu ăn! ⏰",
            "en": "Time to cook! ⏰"
        },
        "body_templates": {
            "vi": "Bạn đã lưu công thức {recipe_name}. Hôm nay là ngày tuyệt vời để thử làm nhé!",
            "en": "You saved the {recipe_name} recipe. Today is a perfect day to try making it!"
        },
        "action_url": "banachef://recipe/{recipe_id}",
        "action_button_text": {
            "vi": "Nấu ngay",
            "en": "Cook Now"
        },
        "priority": "normal",
        "badge_count": False,
        "is_active": True
    }
]


def seed_templates():
    """Seed notification templates into database"""
    db = SessionLocal()
    
    try:
        created_count = 0
        updated_count = 0
        
        for template_data in DEFAULT_TEMPLATES:
            # Check if template already exists
            existing = notification_template.get_by_key(
                db, template_key=template_data["template_key"]
            )
            
            if existing:
                print(f"✅ Template '{template_data['template_key']}' already exists")
                updated_count += 1
            else:
                # Create new template
                notification_template.create_template(
                    db, obj_in=NotificationTemplateCreate(**template_data)
                )
                print(f"✅ Created template: {template_data['template_key']}")
                created_count += 1
        
        print(f"\n🎉 Seeding completed!")
        print(f"   - Created: {created_count} templates")
        print(f"   - Existing: {updated_count} templates")
        print(f"   - Total: {len(DEFAULT_TEMPLATES)} templates")
        
    except Exception as e:
        print(f"❌ Error seeding templates: {e}")
        db.rollback()
    finally:
        db.close()


if __name__ == "__main__":
    print("🚀 Seeding notification templates...")
    seed_templates()
