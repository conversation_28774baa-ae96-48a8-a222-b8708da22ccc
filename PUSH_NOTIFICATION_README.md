# 🔔 Push Notification System - Bana Chef

Comprehensive push notification system for Bana Chef using Firebase Cloud Messaging (FCM) with template-based notifications, device management, and analytics.

## 🚀 Features

- ✅ **Firebase Cloud Messaging (FCM)** integration
- ✅ **Template-based notifications** with multi-language support
- ✅ **Device token management** (iOS, Android, Web)
- ✅ **Background task processing** with Celery
- ✅ **Scheduled notifications** (daily, weekly)
- ✅ **Real-time analytics** and delivery tracking
- ✅ **Business logic integration** (referrals, welcome, etc.)
- ✅ **Admin dashboard** for template management
- ✅ **Rate limiting** and error handling

## 📋 Table of Contents

1. [Quick Start](#quick-start)
2. [Architecture](#architecture)
3. [API Endpoints](#api-endpoints)
4. [Notification Templates](#notification-templates)
5. [Background Tasks](#background-tasks)
6. [Analytics](#analytics)
7. [Configuration](#configuration)
8. [Deployment](#deployment)
9. [Testing](#testing)
10. [Troubleshooting](#troubleshooting)

## 🚀 Quick Start

### 1. Setup Firebase

1. Create a Firebase project at [Firebase Console](https://console.firebase.google.com/)
2. Enable Firebase Cloud Messaging (FCM)
3. Generate service account key:
   ```bash
   # Download service account JSON file
   # Place it at: credentials/firebase-service-account.json
   ```

### 2. Install Dependencies

```bash
# Install new dependencies
pip install -r requirements.txt

# Or using make
make install
```

### 3. Environment Configuration

```bash
# Add to .env file
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_CREDENTIALS_PATH=credentials/firebase-service-account.json
REDIS_URL=redis://redis:6379/0
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0
```

### 4. Database Setup

```bash
# Create tables
make migrate

# Seed notification templates
make seed-templates
```

### 5. Start Services

```bash
# Start all services with Docker
make up

# Or start individual components
make celery-worker    # Background tasks
make celery-beat      # Scheduled tasks
make celery-flower    # Monitoring (optional)
```

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client Apps   │    │  FastAPI Server │    │ Background Jobs │
│                 │    │                 │    │                 │
│ iOS/Android/Web │◄──►│ Notification    │◄──►│ Celery Workers  │
│                 │    │ APIs            │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │ Firebase FCM    │    │ Redis Queue     │
                       │                 │    │                 │
                       │ Push Delivery   │    │ Task Storage    │
                       └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │ PostgreSQL DB   │
                       │                 │
                       │ Templates, Logs │
                       │ Device Tokens   │
                       └─────────────────┘
```

## 📱 API Endpoints

### Device Management

```http
# Register device token
POST /api/v1/notifications/devices/register
Content-Type: application/json
Authorization: Bearer <token>

{
  "device_token": "fcm_device_token_here",
  "device_type": "ios",
  "device_id": "iPhone14,2",
  "app_version": "1.0.0",
  "os_version": "16.0"
}

# Get user devices
GET /api/v1/notifications/devices

# Deactivate device
DELETE /api/v1/notifications/devices/{token_id}

# Test notification
POST /api/v1/notifications/test
{
  "title": "Test Notification",
  "body": "This is a test!",
  "device_token_id": "optional-specific-device"
}
```

### Admin Template Management

```http
# Get templates
GET /api/v1/notifications/admin/templates?category=marketing

# Create template
POST /api/v1/notifications/admin/templates
{
  "template_key": "new_recipe_alert",
  "template_name": "New Recipe Alert",
  "category": "marketing",
  "notification_type": "recipe_alert",
  "title_templates": {
    "vi": "Công thức mới: {recipe_name}! 🍳",
    "en": "New recipe: {recipe_name}! 🍳"
  },
  "body_templates": {
    "vi": "Khám phá công thức {recipe_name} mới toanh!",
    "en": "Discover the brand new {recipe_name} recipe!"
  }
}

# Send notification
POST /api/v1/notifications/admin/send
{
  "template_key": "new_recipe_alert",
  "user_ids": ["user-uuid-1", "user-uuid-2"],
  "variables": {
    "recipe_name": "Phở Gà"
  },
  "language": "vi"
}
```

### Analytics

```http
# Get analytics overview
GET /api/v1/notifications/admin/analytics/overview?days=30

# Template performance
GET /api/v1/notifications/admin/analytics/templates?days=7

# Daily statistics
GET /api/v1/notifications/admin/analytics/daily?days=30

# User engagement
GET /api/v1/notifications/my-engagement?days=30

# Track click
POST /api/v1/notifications/click
{
  "log_id": "notification-log-uuid"
}
```

## 📝 Notification Templates

### Built-in Templates

1. **welcome_new_user** - Welcome message for new users
2. **referral_reward** - Referral reward notification
3. **recipe_suggestion** - Daily recipe suggestions
4. **weekly_summary** - Weekly cooking summary
5. **user_reactivation** - Re-engagement for inactive users
6. **cooking_reminder** - Reminder to cook saved recipes

### Template Variables

Templates support Jinja2 syntax with variables:

```json
{
  "title_templates": {
    "vi": "Chào {user_name}! 👋",
    "en": "Hello {user_name}! 👋"
  },
  "body_templates": {
    "vi": "Bạn có {recipe_count} công thức mới!",
    "en": "You have {recipe_count} new recipes!"
  }
}
```

### Creating Custom Templates

```python
# Example: Create birthday notification template
template_data = {
    "template_key": "birthday_greeting",
    "template_name": "Birthday Greeting",
    "category": "marketing",
    "notification_type": "birthday",
    "title_templates": {
        "vi": "Chúc mừng sinh nhật {user_name}! 🎂",
        "en": "Happy Birthday {user_name}! 🎂"
    },
    "body_templates": {
        "vi": "Hôm nay là ngày đặc biệt! Hãy nấu món {favorite_dish} yêu thích nhé!",
        "en": "Today is special! How about cooking your favorite {favorite_dish}!"
    },
    "action_url": "banachef://birthday-recipes",
    "priority": "high",
    "badge_count": True
}
```

## ⚙️ Background Tasks

### Immediate Tasks

```python
# Trigger welcome notification
from app.tasks.notification_tasks import trigger_welcome_notification
trigger_welcome_notification(user_id)

# Trigger referral reward
from app.tasks.notification_tasks import trigger_referral_reward_notification
trigger_referral_reward_notification(user_id, "John Doe", 5.0)
```

### Scheduled Tasks

- **Daily Recipe Suggestions** - 9:00 AM UTC daily
- **Weekly Cooking Summary** - Monday weekly
- **User Reactivation** - Every 3 days
- **Cleanup Old Logs** - Daily maintenance

### Custom Scheduling

```python
# Add custom scheduled task
from app.core.celery_app import celery_app

@celery_app.task
def custom_notification_task():
    # Your custom logic here
    pass

# Add to beat schedule in celery_app.py
beat_schedule = {
    "custom-task": {
        "task": "app.tasks.custom_tasks.custom_notification_task",
        "schedule": 60.0 * 60.0 * 6.0,  # Every 6 hours
    }
}
```

## 📊 Analytics

### Metrics Tracked

- **Delivery Rate** - Successfully sent notifications
- **Click-through Rate** - User engagement with notifications
- **Template Performance** - Which templates work best
- **Daily/Weekly Trends** - Usage patterns over time
- **User Engagement** - Individual user interaction metrics

### Analytics API Response

```json
{
  "success": true,
  "analytics": {
    "total_notifications": 1000,
    "sent_count": 950,
    "success_rate": 95.0,
    "click_rate": 15.5,
    "status_breakdown": {
      "sent": 800,
      "delivered": 100,
      "clicked": 50,
      "failed": 50
    }
  }
}
```

## 🔧 Configuration

### Environment Variables

```bash
# Firebase Configuration
FIREBASE_PROJECT_ID=banachef-app
FIREBASE_CREDENTIALS_PATH=credentials/firebase-service-account.json

# Redis Configuration
REDIS_URL=redis://redis:6379/0
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0

# Notification Settings
NOTIFICATION_BATCH_SIZE=100
NOTIFICATION_RETRY_ATTEMPTS=3
NOTIFICATION_DEFAULT_LANGUAGE=vi
NOTIFICATION_RATE_LIMIT_PER_USER=50

# Scheduled Tasks
ENABLE_SCHEDULED_NOTIFICATIONS=true
DAILY_RECIPE_SUGGESTION_TIME=09:00
WEEKLY_SUMMARY_DAY=0
```

### Rate Limiting

- **Per User**: 50 notifications per hour
- **Batch Size**: 100 notifications per batch
- **Retry Logic**: 3 attempts with exponential backoff

## 🚀 Deployment

### Production Setup

1. **Firebase Setup**
   ```bash
   # Use service account for production
   export GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account.json
   ```

2. **Docker Deployment**
   ```bash
   # Production docker-compose
   docker-compose -f docker-compose.prod.yml up -d
   ```

3. **Monitoring**
   ```bash
   # Start Flower monitoring
   make flower-up
   
   # Access at http://localhost:5555
   ```

### Health Checks

```bash
# Check API health
curl http://localhost:8000/health

# Check Celery workers
celery -A app.core.celery_app inspect active

# Check Redis
redis-cli ping

## 🧪 Testing

### Unit Tests

```bash
# Run notification tests
pytest tests/test_notifications.py -v

# Run with coverage
pytest tests/test_notifications.py --cov=app.services.notification_service

# Test specific functionality
pytest tests/test_notifications.py::test_send_notification -v
```

### Integration Tests

```bash
# Test FCM integration (requires Firebase setup)
pytest tests/test_firebase_integration.py -v

# Test Celery tasks
pytest tests/test_notification_tasks.py -v

# Test API endpoints
pytest tests/test_notification_api.py -v
```

### Manual Testing

```bash
# 1. Register device token
curl -X POST http://localhost:8000/api/v1/notifications/devices/register \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "device_token": "test_fcm_token",
    "device_type": "ios",
    "device_id": "test_device"
  }'

# 2. Send test notification
curl -X POST http://localhost:8000/api/v1/notifications/test \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Test Notification",
    "body": "Testing push notifications!"
  }'

# 3. Check analytics
curl -X GET http://localhost:8000/api/v1/notifications/admin/analytics/overview \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

### Load Testing

```bash
# Install locust
pip install locust

# Run load test
locust -f tests/load_test_notifications.py --host=http://localhost:8000
```

## 🔍 Troubleshooting

### Common Issues

#### 1. Firebase Connection Issues

```bash
# Check Firebase credentials
export GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account.json
python -c "import firebase_admin; print('Firebase OK')"

# Verify project ID
firebase projects:list
```

#### 2. Celery Worker Not Processing Tasks

```bash
# Check Redis connection
redis-cli ping

# Check Celery worker status
celery -A app.core.celery_app inspect active

# Restart workers
docker-compose restart celery_worker celery_beat
```

#### 3. Notifications Not Delivered

```bash
# Check FCM token validity
# Invalid tokens are automatically deactivated

# Check notification logs
curl -X GET http://localhost:8000/api/v1/notifications/admin/analytics/overview

# Check failed notifications
SELECT * FROM notification_logs WHERE delivery_status = 'failed' ORDER BY created_at DESC LIMIT 10;
```

#### 4. High Memory Usage

```bash
# Monitor Celery workers
celery -A app.core.celery_app inspect stats

# Adjust worker concurrency
celery -A app.core.celery_app worker --concurrency=1

# Check Redis memory usage
redis-cli info memory
```

### Debug Mode

```bash
# Enable debug logging
export LOG_LEVEL=DEBUG

# Check logs
docker-compose logs -f celery_worker
docker-compose logs -f api
```

### Performance Optimization

1. **Batch Processing**
   ```python
   # Send to multiple users efficiently
   await notification_service.send_batch_notification(
       db, user_ids=user_list, template_key="recipe_suggestion"
   )
   ```

2. **Rate Limiting**
   ```python
   # Implement rate limiting per user
   NOTIFICATION_RATE_LIMIT_PER_USER = 50  # per hour
   ```

3. **Database Indexing**
   ```sql
   -- Ensure proper indexes exist
   CREATE INDEX idx_notification_logs_user_created ON notification_logs(user_id, created_at);
   CREATE INDEX idx_device_tokens_user_active ON device_tokens(user_id, is_active);
   ```

## 📈 Monitoring & Alerts

### Flower Dashboard

Access Celery monitoring at: `http://localhost:5555`

- Monitor task execution
- View worker status
- Check queue lengths
- Analyze task performance

### Prometheus Metrics (Optional)

```yaml
# Add to monitoring/prometheus.yml
- job_name: 'banachef-notifications'
  static_configs:
    - targets: ['api:8000']
  metrics_path: '/metrics'
```

### Alert Rules

```yaml
# monitoring/alert_rules.yml
- alert: HighNotificationFailureRate
  expr: (notification_failures / notification_total) > 0.1
  for: 5m
  labels:
    severity: warning
  annotations:
    summary: "High notification failure rate detected"

- alert: CeleryWorkerDown
  expr: celery_workers_active == 0
  for: 1m
  labels:
    severity: critical
  annotations:
    summary: "No active Celery workers"
```

## 🔐 Security Considerations

### Firebase Security

1. **Service Account Permissions**
   - Use minimal required permissions
   - Rotate service account keys regularly
   - Store credentials securely

2. **Token Validation**
   ```python
   # Validate device tokens before storing
   is_valid = await firebase_service.validate_token(device_token)
   ```

### API Security

1. **Authentication Required**
   - All endpoints require valid JWT tokens
   - Admin endpoints need elevated permissions

2. **Rate Limiting**
   ```python
   # Implement per-user rate limiting
   @router.post("/notifications/send")
   @rate_limit("50/hour")
   async def send_notification(...):
   ```

3. **Input Validation**
   ```python
   # Validate all input data
   class NotificationRequest(BaseModel):
       title: str = Field(..., max_length=100)
       body: str = Field(..., max_length=500)
   ```

## 🚀 Advanced Features

### Custom Notification Channels

```python
# Extend for SMS, Email, etc.
class MultiChannelNotificationService:
    async def send_notification(self, channels: List[str], ...):
        if "push" in channels:
            await self.send_push_notification(...)
        if "email" in channels:
            await self.send_email_notification(...)
        if "sms" in channels:
            await self.send_sms_notification(...)
```

### A/B Testing

```python
# Template A/B testing
@celery_app.task
def send_ab_test_notification(user_id: str, template_a: str, template_b: str):
    # Randomly assign users to template variants
    template = random.choice([template_a, template_b])
    # Track which template was used
    # Analyze performance differences
```

### Personalization Engine

```python
# AI-powered personalization
class PersonalizationService:
    def get_optimal_send_time(self, user_id: UUID) -> datetime:
        # Analyze user behavior patterns
        # Return optimal notification time

    def get_personalized_content(self, user_id: UUID, template: str) -> dict:
        # Generate personalized variables based on user profile
        # Return customized template variables
```

## 📚 Additional Resources

- [Firebase Cloud Messaging Documentation](https://firebase.google.com/docs/cloud-messaging)
- [Celery Documentation](https://docs.celeryproject.org/)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [Redis Documentation](https://redis.io/documentation)

## 🤝 Contributing

1. Fork the repository
2. Create feature branch: `git checkout -b feature/notification-enhancement`
3. Add tests for new functionality
4. Update documentation
5. Submit pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**🎉 Congratulations! Your push notification system is now ready for production use!**

For support or questions, please create an issue in the repository or contact the development team.
```
