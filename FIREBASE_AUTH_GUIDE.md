# Firebase Authentication Integration Guide

## Overview

This guide explains how to use the new Firebase Authentication integration in the BanaChef server. The system now supports both traditional OAuth tokens and Firebase ID tokens for authentication.

## Architecture

### Client-Side Flow
1. User authenticates with Firebase Auth (Google/Apple/etc.)
2. Client receives Firebase ID token
3. Client sends Firebase ID token to server
4. Server verifies token and returns JWT tokens for session management

### Server-Side Flow
1. Server receives Firebase ID token
2. Verifies token with Firebase Admin SDK
3. Creates/updates user in database
4. Issues internal JWT tokens (access + refresh)
5. Client uses JWT tokens for subsequent API calls

## API Endpoints

### 1. Firebase Login
```http
POST /api/v1/auth/login/firebase
Content-Type: application/json

{
  "firebase_token": "eyJhbGciOiJSUzI1NiIs..."
}
```

**Response:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "user": {
    "user_id": "uuid-here",
    "email": "<EMAIL>",
    "display_name": "User Name",
    "photo_url": "https://...",
    "auth_provider": "google",
    "referral_code": "REF123",
    "created_at": "2024-01-01T00:00:00",
    "last_login_at": "2024-01-01T00:00:00"
  }
}
```

### 2. Enhanced Google/Apple Login
The existing Google and Apple endpoints now support both traditional OAuth tokens and Firebase tokens:

```http
POST /api/v1/auth/login/google
POST /api/v1/auth/login/apple
```

These endpoints will:
1. First try to verify as Firebase token
2. Fallback to traditional OAuth verification if Firebase fails
3. Return the same response format

### 3. Refresh Token
```http
POST /api/v1/auth/refresh
Content-Type: application/json

{
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

## Authentication Middleware

The authentication middleware now supports both token types:

### JWT Tokens (Internal)
- Used for session management
- Shorter lifespan (configurable, default 1 hour)
- Verified using internal JWT secret

### Firebase ID Tokens
- Issued by Firebase
- Verified using Firebase Admin SDK
- Automatically refreshed by Firebase client SDKs

## Client Implementation Examples

### JavaScript/React
```javascript
import { getAuth, signInWithPopup, GoogleAuthProvider } from 'firebase/auth';

// Initialize Firebase Auth
const auth = getAuth();
const provider = new GoogleAuthProvider();

// Sign in with Google
const signInWithGoogle = async () => {
  try {
    const result = await signInWithPopup(auth, provider);
    const idToken = await result.user.getIdToken();
    
    // Send to your server
    const response = await fetch('/api/v1/auth/login/firebase', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        firebase_token: idToken
      })
    });
    
    const data = await response.json();
    // Store JWT tokens for API calls
    localStorage.setItem('access_token', data.access_token);
    localStorage.setItem('refresh_token', data.refresh_token);
  } catch (error) {
    console.error('Authentication failed:', error);
  }
};
```

### iOS/Swift
```swift
import FirebaseAuth

// Sign in with Google
Auth.auth().signIn(with: credential) { result, error in
  guard let user = result?.user else { return }
  
  user.getIDToken { idToken, error in
    guard let idToken = idToken else { return }
    
    // Send to your server
    let request = URLRequest(url: URL(string: "https://your-api.com/api/v1/auth/login/firebase")!)
    // ... configure request with idToken
  }
}
```

### Android/Kotlin
```kotlin
import com.google.firebase.auth.FirebaseAuth

// After successful Firebase authentication
val user = FirebaseAuth.getInstance().currentUser
user?.getIdToken(true)?.addOnCompleteListener { task ->
    if (task.isSuccessful) {
        val idToken = task.result?.token
        // Send to your server
    }
}
```

## Configuration

### Environment Variables
```env
# Firebase Configuration
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_CREDENTIALS_PATH=path/to/firebase-service-account.json

# JWT Configuration (existing)
JWT_SECRET_KEY=your-jwt-secret-key
ACCESS_TOKEN_EXPIRE_MINUTES=60
```

### Firebase Service Account
1. Go to Firebase Console → Project Settings → Service Accounts
2. Generate new private key
3. Download JSON file
4. Place in `credentials/firebase-service-account.json`
5. Set `FIREBASE_CREDENTIALS_PATH` environment variable

## Migration Strategy

### For Existing Clients
1. **Immediate**: Existing OAuth endpoints continue to work
2. **Gradual**: Update clients to use Firebase Auth
3. **Future**: Deprecate direct OAuth endpoints

### For New Clients
- Use Firebase Auth from the start
- Simpler implementation
- Better security and scalability

## Error Handling

### Common Error Responses

**Invalid Firebase Token:**
```json
{
  "detail": "Invalid Firebase token",
  "status_code": 401
}
```

**Email Not Verified:**
```json
{
  "detail": "Email not verified",
  "status_code": 401
}
```

**User Not Found:**
```json
{
  "detail": "User not found",
  "status_code": 401
}
```

## Testing

### Run Integration Tests
```bash
# Run unit tests
python -m pytest tests/test_firebase_auth.py -v

# Run integration test script
python scripts/test_firebase_integration.py
```

### Manual Testing
1. Set up Firebase project with authentication
2. Configure service account credentials
3. Use Firebase Auth in a test client
4. Verify token exchange works correctly

## Security Considerations

### Token Validation
- Firebase tokens are verified using Firebase Admin SDK
- JWT tokens use internal secret key
- Both token types have expiration times

### User Mapping
- Firebase UID is stored as `provider_user_id`
- Users are uniquely identified by Firebase UID
- Email changes are handled automatically

### Best Practices
1. Always verify email is confirmed
2. Use HTTPS for all token exchanges
3. Implement proper token refresh logic
4. Monitor for suspicious authentication patterns

## Troubleshooting

### Common Issues

**Firebase Admin SDK Not Initialized:**
- Check `FIREBASE_CREDENTIALS_PATH`
- Verify service account JSON file exists
- Ensure proper permissions

**Token Verification Fails:**
- Check Firebase project ID matches
- Verify token hasn't expired
- Ensure client and server use same Firebase project

**User Not Found After Authentication:**
- Check database connection
- Verify user creation logic
- Check `provider_user_id` mapping

### Debug Mode
Enable debug logging:
```python
import logging
logging.getLogger('app.services.firebase_auth_service').setLevel(logging.DEBUG)
```

## Support

For issues related to Firebase Auth integration:
1. Check this documentation
2. Run the integration test script
3. Check server logs for detailed error messages
4. Verify Firebase console configuration
