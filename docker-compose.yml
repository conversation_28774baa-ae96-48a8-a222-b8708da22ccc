services:
  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    container_name: banachef_db
    environment:
      POSTGRES_DB: banachef_db
      POSTGRES_USER: ${DB_USER:-banachef_admin}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-123453Ago@}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-banachef_admin} -d banachef_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # FastAPI Application
  api:
    build: .
    container_name: banachef_api
    environment:
      - DATABASE_URL=postgresql://${DB_USER:-banachef_admin}:${DB_PASSWORD:-123453Ago@}@db:5432/banachef_db
      - SECRET_KEY=${SECRET_KEY:-dev-secret-key-change-in-production}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-dev-jwt-secret-key-change-in-production}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY:-}
      - DEBUG=${DEBUG:-True}
      # Redis & Celery
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      # Firebase & Notifications
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID:-banachef}
      - FIREBASE_CREDENTIALS_PATH=/app/credentials/firebase-service-account.json
      - NOTIFICATION_BATCH_SIZE=${NOTIFICATION_BATCH_SIZE:-100}
      - NOTIFICATION_RETRY_ATTEMPTS=${NOTIFICATION_RETRY_ATTEMPTS:-3}
      - NOTIFICATION_DEFAULT_LANGUAGE=${NOTIFICATION_DEFAULT_LANGUAGE:-vi}
    ports:
      - "8000:8000"
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./app:/app/app
      - ./.env:/app/.env
      - ./credentials:/app/credentials
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # Redis (for caching and Celery broker)
  redis:
    image: redis:7-alpine
    container_name: banachef_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Celery Worker
  celery_worker:
    build: .
    container_name: banachef_celery_worker
    environment:
      - DATABASE_URL=postgresql://${DB_USER:-banachef_admin}:${DB_PASSWORD:-123453Ago@}@db:5432/banachef_db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID:-banachef}
      - FIREBASE_CREDENTIALS_PATH=/app/credentials/firebase-service-account.json
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./app:/app/app
      - ./credentials:/app/credentials
    command: python run_celery.py worker

  # Celery Beat Scheduler
  celery_beat:
    build: .
    container_name: banachef_celery_beat
    environment:
      - DATABASE_URL=postgresql://${DB_USER:-banachef_admin}:${DB_PASSWORD:-123453Ago@}@db:5432/banachef_db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./app:/app/app
      - celery_beat_data:/app/celerybeat-schedule
    command: python run_celery.py beat

  # Flower (Celery monitoring) - optional
  flower:
    build: .
    container_name: banachef_flower
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    ports:
      - "5555:5555"
    depends_on:
      redis:
        condition: service_healthy
    volumes:
      - ./app:/app/app
    command: python run_celery.py flower
    profiles:
      - monitoring

volumes:
  postgres_data:
  redis_data:
  celery_beat_data:
