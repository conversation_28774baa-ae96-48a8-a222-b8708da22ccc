# BanaChef AI - Referral System Implementation

## Tổng quan

Hệ thống giới thiệu bạn bè (Referral System) cho BanaChef AI được thiết kế theo tài liệu đặc tả nghiệp vụ với các quy tắc phức tạp và logic backend hoàn chỉnh.

## Kiến trúc Hệ thống

### Database Schema

#### Bảng `users` (đã cập nhật)
```sql
-- Các trường mới được thêm vào
referral_code VARCHAR(50) UNIQUE,           -- Mã giới thiệu cá nhân
referred_by_user_id UUID REFERENCES users(user_id), -- Người giới thiệu
has_made_first_purchase BOOLEAN DEFAULT FALSE       -- Đ<PERSON> mua lần đầu chưa
```

#### Bảng `referral_history`
```sql
id UUID PRIMARY KEY,
referrer_user_id UUID REFERENCES users(user_id),    -- <PERSON><PERSON><PERSON><PERSON> giới thiệ<PERSON>
referred_user_id UUID REFERENCES users(user_id),    -- Ng<PERSON><PERSON><PERSON> đ<PERSON>c giới thiệu
status VARCHAR(20) DEFAULT 'pending',               -- pending, completed
created_at TIMESTAMP WITH TIME ZONE
```

#### Bảng `referral_vouchers`
```sql
voucher_id UUID PRIMARY KEY,
owner_user_id UUID REFERENCES users(user_id),       -- Chủ sở hữu voucher
source_referral_id UUID REFERENCES referral_history(id), -- Nguồn gốc
amount_usd NUMERIC(5,2) DEFAULT 5.00,              -- Giá trị voucher
status VARCHAR(20) DEFAULT 'available',             -- available, used, expired
created_at TIMESTAMP WITH TIME ZONE,
used_at TIMESTAMP WITH TIME ZONE,
expires_at TIMESTAMP WITH TIME ZONE                 -- Hết hạn sau 1 năm
```

## Quy tắc Nghiệp vụ

### Người dùng mới (B)
- **B1**: Chỉ được nhập mã giới thiệu của một người duy nhất
- **B2**: Nhận giảm giá 5$ cho lần mua đầu tiên

### Người giới thiệu (A)
- **A1**: Tích lũy 5$ cho mỗi người giới thiệu thành công
- **A2**: Nếu A cũng được giới thiệu, có thể kết hợp 2 ưu đãi (tối đa 10$) cho lần mua đầu
- **A3**: Sau khi mua lần đầu, không thể nhập mã giới thiệu nữa

## API Endpoints

### 1. Apply Referral Code
```http
POST /api/v1/referrals/apply
Authorization: Bearer <token>
Content-Type: application/json

{
  "referral_code": "BANACHEF-NGUYEN123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Successfully applied referral code from John Doe",
  "referrer_display_name": "John Doe"
}
```

### 2. Get Purchase Offerings
```http
GET /api/v1/subscriptions/purchase-offerings
Authorization: Bearer <token>
```

**Response:**
```json
{
  "offerings": [
    {
      "package_id": "weekly",
      "package_name": "Weekly Premium",
      "base_price_usd": 5.00,
      "final_price_usd": 0.00,
      "discount_amount_usd": 5.00
    },
    {
      "package_id": "yearly",
      "package_name": "Yearly Premium",
      "base_price_usd": 50.00,
      "final_price_usd": 40.00,
      "discount_amount_usd": 10.00
    }
  ],
  "applied_vouchers": ["uuid-of-voucher"],
  "new_user_discount_applied": true,
  "total_discount_usd": 10.00
}
```

### 3. Get Referral Stats
```http
GET /api/v1/referrals/stats
Authorization: Bearer <token>
```

**Response:**
```json
{
  "referral_code": "BANACHEF-NGUYEN123",
  "total_referrals": 5,
  "completed_referrals": 3,
  "pending_referrals": 2,
  "available_vouchers": 2,
  "total_voucher_value_usd": 10.00
}
```

### 4. Payment Success Webhook
```http
POST /api/v1/subscriptions/webhook/payment-success
Content-Type: application/json

{
  "user_id": "uuid",
  "package_id": "monthly",
  "amount_paid_usd": 15.00,
  "applied_vouchers": ["voucher-uuid"],
  "transaction_id": "txn_123",
  "payment_provider": "google_play"
}
```

## Triển khai

### 1. Migration Database
```bash
# Chạy migration script
python migrate_referral_system.py
```

### 2. Tạo Tables (cho setup mới)
```bash
# Tạo tất cả tables bao gồm referral
python create_tables.py
```

### 3. Chạy Tests
```bash
# Unit tests
pytest tests/test_referral_system.py -v

# Integration tests
python test_referral_integration.py
```

### 4. Khởi động Server
```bash
# Development
uvicorn app.main:app --reload

# Production
docker-compose -f docker-compose.prod.yml up -d
```

## Luồng Hoạt động

### Luồng 1: Người dùng mới áp dụng mã
1. User đăng ký tài khoản → Hệ thống tự động sinh mã giới thiệu
2. User nhập mã giới thiệu → Validate và tạo relationship
3. Tạo record trong `referral_history` với status "pending"

### Luồng 2: Kiểm tra ưu đãi trước thanh toán
1. Client gọi `/subscriptions/purchase-offerings`
2. Hệ thống tính toán:
   - New user discount (5$ nếu được giới thiệu)
   - Referral voucher discount (5$ nếu có voucher)
3. Trả về danh sách gói với giá đã giảm

### Luồng 3: Xử lý sau thanh toán
1. Nhận webhook từ payment provider
2. Mark user `has_made_first_purchase = true`
3. Mark applied vouchers as "used"
4. Nếu có referrer:
   - Mark referral history as "completed"
   - Tạo voucher mới cho referrer

## Monitoring & Logging

### Key Metrics
- Tổng số referrals
- Conversion rate (pending → completed)
- Voucher usage rate
- Revenue impact

### Error Handling
- Duplicate referral code application
- Self-referral attempts
- Invalid referral codes
- Expired vouchers

## Security Considerations

1. **Webhook Validation**: Verify signatures từ payment providers
2. **Rate Limiting**: Prevent abuse của referral code application
3. **Input Validation**: Sanitize tất cả inputs
4. **Transaction Safety**: Sử dụng database transactions

## Troubleshooting

### Common Issues

1. **Referral code không unique**
   - Solution: Script tự động retry với số khác

2. **User không nhận được discount**
   - Check: `has_made_first_purchase` status
   - Check: `referred_by_user_id` relationship

3. **Voucher không được tạo cho referrer**
   - Check: Referral history status
   - Check: Webhook processing logs

### Debug Commands
```bash
# Check user referral status
SELECT referral_code, referred_by_user_id, has_made_first_purchase 
FROM users WHERE email = '<EMAIL>';

# Check available vouchers
SELECT * FROM referral_vouchers 
WHERE owner_user_id = 'user-uuid' AND status = 'available';

# Check referral history
SELECT * FROM referral_history 
WHERE referrer_user_id = 'user-uuid' OR referred_user_id = 'user-uuid';
```

## Tính năng Tương lai

1. **Referral Tiers**: Nhiều cấp độ giới thiệu
2. **Custom Voucher Values**: Voucher với giá trị khác nhau
3. **Expiration Policies**: Chính sách hết hạn linh hoạt
4. **Analytics Dashboard**: Dashboard theo dõi hiệu suất
5. **Social Sharing**: Tích hợp chia sẻ mạng xã hội

---

**Liên hệ**: Nếu có vấn đề về referral system, vui lòng tạo issue hoặc liên hệ team development.
