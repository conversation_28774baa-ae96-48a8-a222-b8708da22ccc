#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create all database tables for Bana Chef application.
This script will create the tables based on the SQLAlchemy models.
"""

import sys
import os

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from sqlalchemy import text
from app.db.session import engine, Base
from app.core.config import settings
import app.models  # This imports all models

def create_uuid_extension():
    """Create UUID extension if it doesn't exist"""
    try:
        with engine.connect() as conn:
            # Check if extension exists
            result = conn.execute(text(
                "SELECT EXISTS(SELECT 1 FROM pg_extension WHERE extname = 'uuid-ossp')"
            ))
            exists = result.scalar()
            
            if not exists:
                print("Creating uuid-ossp extension...")
                conn.execute(text("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\""))
                conn.commit()
                print("✅ UUID extension created successfully")
            else:
                print("✅ UUID extension already exists")
                
    except Exception as e:
        print(f"❌ Error creating UUID extension: {e}")
        return False
    return True

def create_unique_constraints():
    """Create additional unique constraints that are not handled by SQLAlchemy"""
    try:
        with engine.connect() as conn:
            # Create unique constraint for users table (auth_provider, provider_user_id)
            # Only when provider_user_id is not NULL
            constraint_sql = """
            CREATE UNIQUE INDEX IF NOT EXISTS uq_users_auth_provider_user_id 
            ON users (auth_provider, provider_user_id) 
            WHERE provider_user_id IS NOT NULL;
            """
            conn.execute(text(constraint_sql))
            conn.commit()
            print("✅ Additional unique constraints created successfully")
            
    except Exception as e:
        print(f"❌ Error creating unique constraints: {e}")
        return False
    return True

def create_tables():
    """Create all tables"""
    try:
        print("Creating all tables...")
        Base.metadata.create_all(bind=engine)
        print("✅ All tables created successfully")
        return True
    except Exception as e:
        print(f"❌ Error creating tables: {e}")
        return False

def main():
    print("🚀 Starting database table creation...")
    print(f"📊 Database URL: {settings.DATABASE_URL}")
    
    # Step 1: Create UUID extension
    if not create_uuid_extension():
        sys.exit(1)
    
    # Step 2: Create all tables
    if not create_tables():
        sys.exit(1)
    
    # Step 3: Create additional constraints
    if not create_unique_constraints():
        sys.exit(1)
    
    print("\n🎉 Database setup completed successfully!")
    print("\n📋 Created tables:")
    print("   - users")
    print("   - user_profiles")
    print("   - user_temporary_states")
    print("   - recipes")
    print("   - ingredients")
    print("   - recipe_ingredients")
    print("   - referral_history")
    print("   - referral_vouchers")

if __name__ == "__main__":
    main()
