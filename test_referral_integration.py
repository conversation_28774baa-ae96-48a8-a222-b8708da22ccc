#!/usr/bin/env python3
"""
Integration test script for Referral System
This script tests the complete referral flow end-to-end
"""

import sys
import os
import requests
import json
from decimal import Decimal

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(__file__))

from app.db.session import SessionLocal
from app.models.user import User
from app.services.referral_service import referral_service
from app.crud.crud_referral import referral_voucher


class ReferralIntegrationTest:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.db = SessionLocal()
        
    def cleanup(self):
        """Cleanup database session"""
        self.db.close()
    
    def test_referral_code_generation(self):
        """Test 1: Referral code generation for new users"""
        print("🧪 Test 1: Referral Code Generation")
        
        try:
            # Create test user
            test_user = User(
                email="<EMAIL>",
                auth_provider="email",
                display_name="Test User"
            )
            self.db.add(test_user)
            self.db.flush()
            
            # Generate referral code
            referral_code = referral_service.generate_referral_code(
                self.db, test_user.display_name
            )
            
            # Update user with referral code
            test_user.referral_code = referral_code
            self.db.add(test_user)
            self.db.commit()
            
            print(f"✅ Generated referral code: {referral_code}")
            print(f"✅ Code format is correct: {referral_code.startswith('BANACHEF-')}")
            
            return test_user, referral_code
            
        except Exception as e:
            print(f"❌ Test 1 failed: {e}")
            self.db.rollback()
            return None, None
    
    def test_apply_referral_code(self, referrer_user, referral_code):
        """Test 2: Apply referral code"""
        print("\n🧪 Test 2: Apply Referral Code")
        
        try:
            # Create new user
            new_user = User(
                email="<EMAIL>",
                auth_provider="email",
                display_name="New User",
                has_made_first_purchase=False
            )
            self.db.add(new_user)
            self.db.flush()
            
            # Apply referral code
            result = referral_service.apply_referral_code(
                self.db, new_user.user_id, referral_code
            )
            
            if result["success"]:
                print(f"✅ Successfully applied referral code")
                print(f"✅ Message: {result['message']}")
                
                # Verify relationship
                self.db.refresh(new_user)
                assert new_user.referred_by_user_id == referrer_user.user_id
                print(f"✅ Referral relationship established")
                
                return new_user
            else:
                print(f"❌ Failed to apply referral code: {result['message']}")
                return None
                
        except Exception as e:
            print(f"❌ Test 2 failed: {e}")
            self.db.rollback()
            return None
    
    def test_purchase_offerings(self, user):
        """Test 3: Get purchase offerings with discounts"""
        print("\n🧪 Test 3: Purchase Offerings with Discounts")
        
        try:
            # Calculate discounts manually (simulating API logic)
            total_discount = Decimal("0.00")
            applied_vouchers = []
            new_user_discount_applied = False
            
            # Check for new user discount
            if not user.has_made_first_purchase and user.referred_by_user_id:
                total_discount += Decimal("5.00")
                new_user_discount_applied = True
                print(f"✅ New user discount applied: $5.00")
            
            # Check for referral voucher discount
            available_voucher = referral_voucher.get_first_available_voucher(self.db, user.user_id)
            if available_voucher:
                total_discount += available_voucher.amount_usd
                applied_vouchers.append(available_voucher.voucher_id)
                print(f"✅ Referral voucher discount applied: ${available_voucher.amount_usd}")
            
            print(f"✅ Total discount: ${total_discount}")
            
            # Test subscription packages
            base_offerings = [
                {"package_id": "weekly", "base_price_usd": Decimal("5.00")},
                {"package_id": "monthly", "base_price_usd": Decimal("15.00")},
                {"package_id": "yearly", "base_price_usd": Decimal("50.00")}
            ]
            
            print("✅ Subscription offerings with discounts:")
            for offering in base_offerings:
                discount_amount = min(total_discount, offering["base_price_usd"])
                final_price = max(Decimal("0.00"), offering["base_price_usd"] - discount_amount)
                print(f"   {offering['package_id']}: ${offering['base_price_usd']} -> ${final_price} (discount: ${discount_amount})")
            
            return {
                "total_discount": total_discount,
                "applied_vouchers": applied_vouchers,
                "new_user_discount_applied": new_user_discount_applied
            }
            
        except Exception as e:
            print(f"❌ Test 3 failed: {e}")
            return None
    
    def test_first_purchase_processing(self, user, applied_vouchers):
        """Test 4: Process first purchase and reward referrer"""
        print("\n🧪 Test 4: First Purchase Processing")
        
        try:
            # Get referrer before purchase
            referrer_id = user.referred_by_user_id
            if referrer_id:
                vouchers_before = referral_voucher.get_available_vouchers(self.db, referrer_id)
                print(f"✅ Referrer vouchers before purchase: {len(vouchers_before)}")
            
            # Process first purchase
            result = referral_service.process_first_purchase(
                self.db, user.user_id, applied_vouchers=applied_vouchers
            )
            
            if result["success"]:
                print(f"✅ Purchase processed successfully")
                print(f"✅ Message: {result['message']}")
                
                # Verify user marked as purchased
                self.db.refresh(user)
                assert user.has_made_first_purchase is True
                print(f"✅ User marked as having made first purchase")
                
                # Check if referrer was rewarded
                if referrer_id and result.get("referrer_rewarded"):
                    vouchers_after = referral_voucher.get_available_vouchers(self.db, referrer_id)
                    print(f"✅ Referrer vouchers after purchase: {len(vouchers_after)}")
                    assert len(vouchers_after) == len(vouchers_before) + 1
                    print(f"✅ Referrer rewarded with new voucher")
                
                return True
            else:
                print(f"❌ Purchase processing failed: {result['message']}")
                return False
                
        except Exception as e:
            print(f"❌ Test 4 failed: {e}")
            self.db.rollback()
            return False
    
    def test_business_rules(self):
        """Test 5: Business rules validation"""
        print("\n🧪 Test 5: Business Rules Validation")
        
        try:
            # Test Rule A3: Cannot apply referral code after first purchase
            purchased_user = User(
                email="<EMAIL>",
                auth_provider="email",
                has_made_first_purchase=True
            )
            self.db.add(purchased_user)
            self.db.flush()
            
            result = referral_service.apply_referral_code(
                self.db, purchased_user.user_id, "BANACHEF-TEST123"
            )
            
            assert result["success"] is False
            assert "before making your first purchase" in result["message"]
            print("✅ Rule A3: Cannot apply code after first purchase - PASSED")
            
            # Test self-referral prevention
            self_user = User(
                email="<EMAIL>",
                auth_provider="email",
                referral_code="BANACHEF-SELF123"
            )
            self.db.add(self_user)
            self.db.flush()
            
            result = referral_service.apply_referral_code(
                self.db, self_user.user_id, "BANACHEF-SELF123"
            )
            
            assert result["success"] is False
            assert "cannot refer yourself" in result["message"]
            print("✅ Self-referral prevention - PASSED")
            
            return True
            
        except Exception as e:
            print(f"❌ Test 5 failed: {e}")
            self.db.rollback()
            return False
    
    def run_all_tests(self):
        """Run all integration tests"""
        print("🚀 Starting Referral System Integration Tests")
        print("=" * 60)
        
        try:
            # Test 1: Referral code generation
            referrer_user, referral_code = self.test_referral_code_generation()
            if not referrer_user:
                return False
            
            # Test 2: Apply referral code
            new_user = self.test_apply_referral_code(referrer_user, referral_code)
            if not new_user:
                return False
            
            # Test 3: Purchase offerings
            offerings_data = self.test_purchase_offerings(new_user)
            if not offerings_data:
                return False
            
            # Test 4: First purchase processing
            purchase_success = self.test_first_purchase_processing(
                new_user, offerings_data["applied_vouchers"]
            )
            if not purchase_success:
                return False
            
            # Test 5: Business rules
            rules_success = self.test_business_rules()
            if not rules_success:
                return False
            
            print("\n" + "=" * 60)
            print("🎉 All integration tests PASSED!")
            print("\n📊 Test Summary:")
            print("   ✅ Referral code generation")
            print("   ✅ Referral code application")
            print("   ✅ Purchase offerings with discounts")
            print("   ✅ First purchase processing")
            print("   ✅ Business rules validation")
            
            return True
            
        except Exception as e:
            print(f"\n❌ Integration tests failed: {e}")
            return False
        finally:
            self.cleanup()


def main():
    """Main function to run integration tests"""
    tester = ReferralIntegrationTest()
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ Integration testing completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Integration testing failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
