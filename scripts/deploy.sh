#!/bin/bash

# Bana Chef Server Deployment Script
# This script handles deployment to staging and production environments

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DOCKER_COMPOSE_FILE="docker-compose.prod.yml"
BACKUP_DIR="/opt/banachef/backups"
LOG_FILE="/var/log/banachef-deploy.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        error "This script should not be run as root for security reasons"
    fi
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if Docker is installed and running
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed"
    fi
    
    if ! docker info &> /dev/null; then
        error "Docker is not running"
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed"
    fi
    
    # Check if .env file exists
    if [[ ! -f "$PROJECT_ROOT/.env" ]]; then
        error ".env file not found. Please create it from .env.production template"
    fi
    
    success "Prerequisites check passed"
}

# Create backup
create_backup() {
    log "Creating database backup..."
    
    # Create backup directory if it doesn't exist
    sudo mkdir -p "$BACKUP_DIR"
    
    # Get database credentials from .env
    source "$PROJECT_ROOT/.env"
    
    # Create backup filename with timestamp
    BACKUP_FILE="$BACKUP_DIR/banachef_backup_$(date +%Y%m%d_%H%M%S).sql"
    
    # Create database backup
    docker-compose -f "$PROJECT_ROOT/$DOCKER_COMPOSE_FILE" exec -T db pg_dump \
        -U "$DB_USER" \
        -d "$DB_NAME" \
        --no-owner \
        --no-privileges \
        --clean \
        --if-exists > "$BACKUP_FILE" || error "Failed to create database backup"
    
    # Compress backup
    gzip "$BACKUP_FILE"
    
    success "Database backup created: ${BACKUP_FILE}.gz"
    
    # Keep only last 7 backups
    find "$BACKUP_DIR" -name "banachef_backup_*.sql.gz" -type f -mtime +7 -delete
}

# Pull latest images
pull_images() {
    log "Pulling latest Docker images..."
    
    cd "$PROJECT_ROOT"
    docker-compose -f "$DOCKER_COMPOSE_FILE" pull || error "Failed to pull Docker images"
    
    success "Docker images pulled successfully"
}

# Run database migrations
run_migrations() {
    log "Running database migrations..."
    
    cd "$PROJECT_ROOT"
    
    # Check if migration script exists
    if [[ -f "migrate.py" ]]; then
        docker-compose -f "$DOCKER_COMPOSE_FILE" exec api python migrate.py || error "Database migration failed"
    elif [[ -f "alembic.ini" ]]; then
        docker-compose -f "$DOCKER_COMPOSE_FILE" exec api alembic upgrade head || error "Alembic migration failed"
    else
        warning "No migration script found, skipping migrations"
    fi
    
    success "Database migrations completed"
}

# Deploy application
deploy() {
    log "Deploying application..."
    
    cd "$PROJECT_ROOT"
    
    # Start services
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d || error "Failed to start services"
    
    # Wait for services to be healthy
    log "Waiting for services to be healthy..."
    sleep 30
    
    # Check if API is responding
    for i in {1..30}; do
        if curl -f http://localhost:8000/health &> /dev/null; then
            success "API is responding"
            break
        fi
        
        if [[ $i -eq 30 ]]; then
            error "API health check failed after 30 attempts"
        fi
        
        log "Waiting for API to be ready... (attempt $i/30)"
        sleep 10
    done
    
    success "Application deployed successfully"
}

# Cleanup old images and containers
cleanup() {
    log "Cleaning up old Docker images and containers..."
    
    # Remove unused images
    docker image prune -f || warning "Failed to prune images"
    
    # Remove unused containers
    docker container prune -f || warning "Failed to prune containers"
    
    # Remove unused volumes (be careful with this)
    # docker volume prune -f
    
    success "Cleanup completed"
}

# Rollback function
rollback() {
    log "Rolling back to previous version..."
    
    cd "$PROJECT_ROOT"
    
    # Stop current services
    docker-compose -f "$DOCKER_COMPOSE_FILE" down
    
    # Restore from backup (this is a simplified version)
    warning "Automatic rollback is not fully implemented. Please restore manually if needed."
    warning "Latest backup is available in $BACKUP_DIR"
    
    error "Rollback completed. Please check the application manually."
}

# Health check
health_check() {
    log "Performing health checks..."
    
    # Check API health
    if ! curl -f http://localhost:8000/health &> /dev/null; then
        error "API health check failed"
    fi
    
    # Check database connectivity
    cd "$PROJECT_ROOT"
    if ! docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T db pg_isready -U "$DB_USER" &> /dev/null; then
        error "Database health check failed"
    fi
    
    # Check Redis connectivity (if using Redis)
    if docker-compose -f "$DOCKER_COMPOSE_FILE" ps redis | grep -q "Up"; then
        if ! docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T redis redis-cli ping &> /dev/null; then
            warning "Redis health check failed"
        fi
    fi
    
    success "All health checks passed"
}

# Main deployment function
main() {
    local environment="${1:-production}"
    
    log "Starting deployment to $environment environment"
    
    # Trap to handle errors
    trap 'error "Deployment failed. Check logs for details."' ERR
    
    check_root
    check_prerequisites
    
    # Create backup before deployment
    create_backup
    
    # Pull latest images
    pull_images
    
    # Run migrations
    run_migrations
    
    # Deploy application
    deploy
    
    # Perform health checks
    health_check
    
    # Cleanup
    cleanup
    
    success "Deployment to $environment completed successfully!"
    log "Application is running at: https://api.banachef.com"
}

# Script usage
usage() {
    echo "Usage: $0 [environment]"
    echo "  environment: staging or production (default: production)"
    echo ""
    echo "Commands:"
    echo "  deploy [env]    - Deploy to environment"
    echo "  rollback        - Rollback to previous version"
    echo "  health          - Perform health checks"
    echo "  backup          - Create database backup"
    echo "  cleanup         - Clean up Docker resources"
    echo ""
    echo "Examples:"
    echo "  $0 deploy production"
    echo "  $0 rollback"
    echo "  $0 health"
}

# Handle command line arguments
case "${1:-deploy}" in
    deploy)
        main "${2:-production}"
        ;;
    rollback)
        rollback
        ;;
    health)
        health_check
        ;;
    backup)
        create_backup
        ;;
    cleanup)
        cleanup
        ;;
    help|--help|-h)
        usage
        ;;
    *)
        echo "Unknown command: $1"
        usage
        exit 1
        ;;
esac
