"""
Pytest configuration and shared fixtures for tests.
"""

import pytest
import sys
import os
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from app.db.session import Base
from app.core.config import settings
import app.models  # Import all models


@pytest.fixture(scope="session")
def test_engine():
    """Create a test database engine."""
    # Use in-memory SQLite for tests
    engine = create_engine(
        "sqlite:///:memory:",
        poolclass=StaticPool,
        connect_args={"check_same_thread": False},
        echo=False
    )
    
    # Create all tables
    Base.metadata.create_all(bind=engine)
    
    yield engine
    
    # Cleanup
    Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def test_db_session(test_engine):
    """Create a test database session."""
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)
    session = TestingSessionLocal()
    
    yield session
    
    session.close()


@pytest.fixture(scope="function")
def clean_db(test_db_session):
    """Clean database after each test."""
    yield test_db_session
    
    # Clean up all tables
    for table in reversed(Base.metadata.sorted_tables):
        test_db_session.execute(text(f"DELETE FROM {table.name}"))
    test_db_session.commit()


# Test data fixtures
@pytest.fixture
def sample_user_data():
    """Sample user data for testing."""
    return {
        "email": "<EMAIL>",
        "auth_provider": "email",
        "display_name": "Test User"
    }


@pytest.fixture
def sample_ingredient_data():
    """Sample ingredient data for testing."""
    return {
        "name": "Test Ingredient",
        "description": "A test ingredient",
        "default_unit": "kg"
    }


@pytest.fixture
def sample_recipe_data():
    """Sample recipe data for testing."""
    return {
        "name": "Test Recipe",
        "description": "A test recipe",
        "instructions": "1. Test step 1\n2. Test step 2",
        "prep_time_minutes": 10,
        "cook_time_minutes": 20,
        "servings": 4,
        "calories_per_serving": 200
    }
