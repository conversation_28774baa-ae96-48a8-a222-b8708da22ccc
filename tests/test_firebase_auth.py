"""
Tests for Firebase Authentication integration
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.services.firebase_auth_service import FirebaseAuthService
from app.core.security import verify_token_flexible, is_firebase_token
from app.crud.user import get_user_by_provider_user_id


client = TestClient(app)


class TestFirebaseAuthService:
    """Test Firebase Auth Service"""
    
    @pytest.fixture
    def firebase_service(self):
        """Create Firebase service instance for testing"""
        with patch('app.services.firebase_auth_service.firebase_admin'):
            service = FirebaseAuthService()
            return service
    
    @pytest.mark.asyncio
    async def test_verify_firebase_token_success(self, firebase_service):
        """Test successful Firebase token verification"""
        # Mock Firebase auth.verify_id_token
        mock_decoded_token = {
            'uid': 'firebase_uid_123',
            'email': '<EMAIL>',
            'email_verified': True,
            'name': 'Test User',
            'picture': 'https://example.com/photo.jpg',
            'firebase': {
                'sign_in_provider': 'google.com'
            }
        }
        
        with patch('firebase_admin.auth.verify_id_token', return_value=mock_decoded_token):
            result = await firebase_service.verify_firebase_token('mock_firebase_token')
            
            assert result is not None
            assert result['uid'] == 'firebase_uid_123'
            assert result['email'] == '<EMAIL>'
            assert result['auth_provider'] == 'google'
            assert result['email_verified'] is True
    
    @pytest.mark.asyncio
    async def test_verify_firebase_token_invalid(self, firebase_service):
        """Test Firebase token verification with invalid token"""
        from firebase_admin import auth
        
        with patch('firebase_admin.auth.verify_id_token', side_effect=auth.InvalidIdTokenError('Invalid token')):
            result = await firebase_service.verify_firebase_token('invalid_token')
            assert result is None
    
    @pytest.mark.asyncio
    async def test_verify_firebase_token_expired(self, firebase_service):
        """Test Firebase token verification with expired token"""
        from firebase_admin import auth
        
        with patch('firebase_admin.auth.verify_id_token', side_effect=auth.ExpiredIdTokenError('Token expired')):
            result = await firebase_service.verify_firebase_token('expired_token')
            assert result is None


class TestFirebaseAuthEndpoints:
    """Test Firebase Auth API endpoints"""
    
    @pytest.mark.asyncio
    async def test_firebase_login_success(self):
        """Test successful Firebase login"""
        mock_user_info = {
            'uid': 'firebase_uid_123',
            'email': '<EMAIL>',
            'email_verified': True,
            'name': 'Test User',
            'picture': 'https://example.com/photo.jpg',
            'auth_provider': 'google',
            'provider_user_id': 'firebase_uid_123'
        }
        
        with patch('app.services.auth.AuthService.verify_firebase_token', return_value=mock_user_info), \
             patch('app.services.auth.AuthService.authenticate_or_create_user') as mock_auth, \
             patch('app.services.auth.AuthService.create_user_tokens') as mock_tokens:
            
            # Mock user object
            mock_user = Mock()
            mock_user.user_id = 'user_123'
            mock_user.email = '<EMAIL>'
            mock_user.display_name = 'Test User'
            mock_user.photo_url = 'https://example.com/photo.jpg'
            mock_user.auth_provider = 'google'
            mock_user.referral_code = 'REF123'
            mock_user.created_at = None
            mock_user.last_login_at = None
            
            mock_auth.return_value = mock_user
            mock_tokens.return_value = {
                'access_token': 'mock_access_token',
                'refresh_token': 'mock_refresh_token',
                'token_type': 'bearer'
            }
            
            response = client.post(
                "/api/v1/auth/login/firebase",
                json={"firebase_token": "mock_firebase_token"}
            )
            
            assert response.status_code == 200
            data = response.json()
            assert 'access_token' in data
            assert 'refresh_token' in data
            assert data['token_type'] == 'bearer'
            assert data['user']['email'] == '<EMAIL>'
    
    @pytest.mark.asyncio
    async def test_firebase_login_invalid_token(self):
        """Test Firebase login with invalid token"""
        with patch('app.services.auth.AuthService.verify_firebase_token', return_value=None):
            response = client.post(
                "/api/v1/auth/login/firebase",
                json={"firebase_token": "invalid_token"}
            )
            
            assert response.status_code == 401
            assert "Invalid Firebase token" in response.json()['detail']
    
    @pytest.mark.asyncio
    async def test_firebase_login_unverified_email(self):
        """Test Firebase login with unverified email"""
        mock_user_info = {
            'uid': 'firebase_uid_123',
            'email': '<EMAIL>',
            'email_verified': False,  # Email not verified
            'name': 'Test User',
            'auth_provider': 'google'
        }
        
        with patch('app.services.auth.AuthService.verify_firebase_token', return_value=mock_user_info):
            response = client.post(
                "/api/v1/auth/login/firebase",
                json={"firebase_token": "mock_firebase_token"}
            )
            
            assert response.status_code == 401
            assert "Email not verified" in response.json()['detail']


class TestTokenVerification:
    """Test token verification logic"""
    
    def test_is_firebase_token_detection(self):
        """Test Firebase token detection"""
        # Mock JWT token (shorter, has our structure)
        jwt_token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************.signature"
        
        # Mock Firebase token (longer, different structure)
        firebase_token = "**********************************************.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.signature"
        
        # Test JWT token detection
        with patch('jose.jwt.decode') as mock_decode:
            mock_decode.return_value = {'sub': 'user_123'}
            assert is_firebase_token(jwt_token) is False
        
        # Test Firebase token detection
        with patch('jose.jwt.decode', side_effect=Exception("Not a JWT")):
            assert is_firebase_token(firebase_token) is True
    
    @pytest.mark.asyncio
    async def test_verify_token_flexible_jwt(self):
        """Test flexible token verification with JWT token"""
        with patch('app.core.security.verify_token', return_value='user_123'):
            result = await verify_token_flexible('mock_jwt_token')
            assert result == 'user_123'
    
    @pytest.mark.asyncio
    async def test_verify_token_flexible_firebase(self):
        """Test flexible token verification with Firebase token"""
        mock_user_info = {'uid': 'firebase_uid_123'}
        
        with patch('app.core.security.verify_token', return_value=None), \
             patch('app.services.firebase_auth_service.firebase_auth_service.verify_firebase_token', return_value=mock_user_info):
            result = await verify_token_flexible('mock_firebase_token')
            assert result == 'firebase_uid_123'


class TestUserLookup:
    """Test user lookup by provider_user_id"""
    
    def test_get_user_by_provider_user_id(self):
        """Test getting user by Firebase UID"""
        mock_db = Mock(spec=Session)
        mock_query = Mock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        
        mock_user = Mock()
        mock_query.first.return_value = mock_user
        
        result = get_user_by_provider_user_id(mock_db, 'firebase_uid_123')
        
        assert result == mock_user
        mock_db.query.assert_called_once()
        mock_query.filter.assert_called_once()
        mock_query.first.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__])
