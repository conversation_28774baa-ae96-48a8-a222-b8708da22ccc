"""
Test database connection and basic operations.
"""

import pytest
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from sqlalchemy import text
from app.models.user import User
from app.models.ingredient import Ingredient


class TestDatabaseConnection:
    """Test database connection and basic operations."""
    
    def test_database_connection(self, test_db_session):
        """Test that database connection works."""
        result = test_db_session.execute(text("SELECT 1"))
        assert result.scalar() == 1
    
    def test_create_user(self, test_db_session, sample_user_data):
        """Test creating a user."""
        user = User(**sample_user_data)
        test_db_session.add(user)
        test_db_session.commit()
        
        # Verify user was created
        created_user = test_db_session.query(User).filter_by(email=sample_user_data["email"]).first()
        assert created_user is not None
        assert created_user.email == sample_user_data["email"]
        assert created_user.display_name == sample_user_data["display_name"]
    
    def test_create_ingredient(self, test_db_session, sample_ingredient_data):
        """Test creating an ingredient."""
        ingredient = Ingredient(**sample_ingredient_data)
        test_db_session.add(ingredient)
        test_db_session.commit()
        
        # Verify ingredient was created
        created_ingredient = test_db_session.query(Ingredient).filter_by(name=sample_ingredient_data["name"]).first()
        assert created_ingredient is not None
        assert created_ingredient.name == sample_ingredient_data["name"]
        assert created_ingredient.description == sample_ingredient_data["description"]
    
    def test_user_email_unique_constraint(self, test_db_session, sample_user_data):
        """Test that user email must be unique."""
        # Create first user
        user1 = User(**sample_user_data)
        test_db_session.add(user1)
        test_db_session.commit()
        
        # Try to create second user with same email
        user2 = User(**sample_user_data)
        test_db_session.add(user2)
        
        with pytest.raises(Exception):  # Should raise integrity error
            test_db_session.commit()
    
    def test_ingredient_name_unique_constraint(self, test_db_session, sample_ingredient_data):
        """Test that ingredient name must be unique."""
        # Create first ingredient
        ingredient1 = Ingredient(**sample_ingredient_data)
        test_db_session.add(ingredient1)
        test_db_session.commit()
        
        # Try to create second ingredient with same name
        ingredient2 = Ingredient(**sample_ingredient_data)
        test_db_session.add(ingredient2)
        
        with pytest.raises(Exception):  # Should raise integrity error
            test_db_session.commit()
