"""
Test SQLAlchemy models.
"""

import pytest
import sys
import os
from datetime import date, datetime
from decimal import Dec<PERSON>l

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from app.models.user import User
from app.models.user_profile import UserProfile
from app.models.user_temporary_state import UserTemporaryState
from app.models.recipe import Recipe
from app.models.ingredient import Ingredient
from app.models.recipe_ingredient import RecipeIngredient


class TestUserModel:
    """Test User model."""
    
    def test_create_user(self, clean_db, sample_user_data):
        """Test creating a user."""
        user = User(**sample_user_data)
        clean_db.add(user)
        clean_db.commit()
        
        assert user.user_id is not None
        assert user.email == sample_user_data["email"]
        assert user.created_at is not None
        assert user.updated_at is not None
    
    def test_user_repr(self, clean_db, sample_user_data):
        """Test user string representation."""
        user = User(**sample_user_data)
        clean_db.add(user)
        clean_db.commit()
        
        repr_str = repr(user)
        assert "User" in repr_str
        assert str(user.user_id) in repr_str
        assert user.email in repr_str


class TestUserProfileModel:
    """Test UserProfile model."""
    
    def test_create_user_profile(self, clean_db, sample_user_data):
        """Test creating a user profile."""
        # First create a user
        user = User(**sample_user_data)
        clean_db.add(user)
        clean_db.flush()
        
        # Create profile
        profile_data = {
            "user_id": user.user_id,
            "date_of_birth": date(1990, 1, 1),
            "gender": "nam",
            "weight_kg": Decimal("70.5"),
            "height_cm": Decimal("175.0"),
            "health_goals": ["giảm cân", "tăng cơ"],
            "food_allergies": ["hải sản"],
            "sweet_preference_level": 3,
            "cooking_skill_level": "cơ bản"
        }
        
        profile = UserProfile(**profile_data)
        clean_db.add(profile)
        clean_db.commit()
        
        assert profile.profile_id is not None
        assert profile.user_id == user.user_id
        assert profile.date_of_birth == date(1990, 1, 1)
        assert profile.health_goals == ["giảm cân", "tăng cơ"]
    
    def test_user_profile_relationship(self, clean_db, sample_user_data):
        """Test relationship between User and UserProfile."""
        # Create user with profile
        user = User(**sample_user_data)
        clean_db.add(user)
        clean_db.flush()
        
        profile = UserProfile(
            user_id=user.user_id,
            cooking_skill_level="cơ bản"
        )
        clean_db.add(profile)
        clean_db.commit()
        
        # Test relationship
        clean_db.refresh(user)
        assert user.profile is not None
        assert user.profile.cooking_skill_level == "cơ bản"


class TestRecipeModel:
    """Test Recipe model."""
    
    def test_create_recipe(self, clean_db, sample_recipe_data):
        """Test creating a recipe."""
        recipe = Recipe(**sample_recipe_data)
        clean_db.add(recipe)
        clean_db.commit()
        
        assert recipe.recipe_id is not None
        assert recipe.name == sample_recipe_data["name"]
        assert recipe.servings == sample_recipe_data["servings"]
        assert recipe.created_at is not None
    
    def test_recipe_with_nutrition_data(self, clean_db):
        """Test recipe with detailed nutrition data."""
        recipe_data = {
            "name": "Healthy Salad",
            "instructions": "Mix vegetables",
            "servings": 2,
            "calories_per_serving": 150,
            "protein_g_per_serving": Decimal("5.5"),
            "carbs_g_per_serving": Decimal("20.0"),
            "fat_g_per_serving": Decimal("8.0"),
            "total_dish_heart_health_indicators": {
                "cholesterol_mg": 0,
                "fiber_g": 8,
                "sodium_mg": 200
            },
            "total_dish_vitamins": {
                "vitamin_c": "cao",
                "vitamin_a": "trung bình"
            }
        }
        
        recipe = Recipe(**recipe_data)
        clean_db.add(recipe)
        clean_db.commit()
        
        assert recipe.calories_per_serving == 150
        assert recipe.protein_g_per_serving == Decimal("5.5")
        assert recipe.total_dish_heart_health_indicators["fiber_g"] == 8


class TestRecipeIngredientModel:
    """Test RecipeIngredient model."""
    
    def test_create_recipe_ingredient(self, clean_db, sample_recipe_data, sample_ingredient_data):
        """Test creating a recipe ingredient relationship."""
        # Create recipe and ingredient
        recipe = Recipe(**sample_recipe_data)
        ingredient = Ingredient(**sample_ingredient_data)
        
        clean_db.add(recipe)
        clean_db.add(ingredient)
        clean_db.flush()
        
        # Create relationship
        recipe_ingredient = RecipeIngredient(
            recipe_id=recipe.recipe_id,
            ingredient_id=ingredient.ingredient_id,
            quantity=Decimal("2.5"),
            unit="cups",
            notes="Fresh ingredients preferred"
        )
        
        clean_db.add(recipe_ingredient)
        clean_db.commit()
        
        assert recipe_ingredient.recipe_ingredient_id is not None
        assert recipe_ingredient.quantity == Decimal("2.5")
        assert recipe_ingredient.unit == "cups"
    
    def test_recipe_ingredients_relationship(self, clean_db, sample_recipe_data, sample_ingredient_data):
        """Test relationship between Recipe and Ingredients."""
        # Create recipe and ingredient
        recipe = Recipe(**sample_recipe_data)
        ingredient = Ingredient(**sample_ingredient_data)
        
        clean_db.add(recipe)
        clean_db.add(ingredient)
        clean_db.flush()
        
        # Create relationship
        recipe_ingredient = RecipeIngredient(
            recipe_id=recipe.recipe_id,
            ingredient_id=ingredient.ingredient_id,
            quantity=Decimal("1.0"),
            unit="kg"
        )
        
        clean_db.add(recipe_ingredient)
        clean_db.commit()
        
        # Test relationships
        clean_db.refresh(recipe)
        assert len(recipe.recipe_ingredients) == 1
        assert recipe.recipe_ingredients[0].ingredient.name == sample_ingredient_data["name"]
