"""
Integration tests for notification API endpoints.
"""

import pytest
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import patch, Mock

from app.main import app
from app.models.user import User
from app.models.device_token import DeviceToken
from app.models.notification_template import NotificationTemplate


class TestDeviceTokenAPI:
    """Test device token management API endpoints"""
    
    @pytest.fixture
    def client(self):
        """Test client"""
        return TestClient(app)
    
    @pytest.fixture
    def auth_headers(self, test_user_token):
        """Authentication headers"""
        return {"Authorization": f"Bearer {test_user_token}"}
    
    def test_register_device_token(self, client, auth_headers, test_db_session):
        """Test device token registration endpoint"""
        device_data = {
            "device_token": "test_fcm_token_123",
            "device_type": "ios",
            "device_id": "iPhone14,2",
            "app_version": "1.0.0",
            "os_version": "16.0"
        }
        
        response = client.post(
            "/api/v1/notifications/devices/register",
            json=device_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "token_id" in data
    
    def test_register_device_token_invalid_type(self, client, auth_headers):
        """Test device token registration with invalid device type"""
        device_data = {
            "device_token": "test_fcm_token_123",
            "device_type": "invalid_type",  # Invalid
            "device_id": "test_device"
        }
        
        response = client.post(
            "/api/v1/notifications/devices/register",
            json=device_data,
            headers=auth_headers
        )
        
        assert response.status_code == 400
        assert "Invalid device_type" in response.json()["detail"]
    
    def test_get_user_devices(self, client, auth_headers, test_db_session, test_user):
        """Test getting user's devices"""
        # Create a device token for the user
        device = DeviceToken(
            user_id=test_user.user_id,
            device_token="existing_token",
            device_type="android",
            is_active=True
        )
        test_db_session.add(device)
        test_db_session.commit()
        
        response = client.get(
            "/api/v1/notifications/devices",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert len(data["devices"]) == 1
        assert data["devices"][0]["device_type"] == "android"
    
    def test_deactivate_device_token(self, client, auth_headers, test_db_session, test_user):
        """Test deactivating a device token"""
        # Create a device token
        device = DeviceToken(
            user_id=test_user.user_id,
            device_token="token_to_deactivate",
            device_type="web",
            is_active=True
        )
        test_db_session.add(device)
        test_db_session.commit()
        test_db_session.refresh(device)
        
        response = client.delete(
            f"/api/v1/notifications/devices/{device.token_id}",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
    
    def test_deactivate_nonexistent_device(self, client, auth_headers):
        """Test deactivating non-existent device token"""
        from uuid import uuid4
        
        response = client.delete(
            f"/api/v1/notifications/devices/{uuid4()}",
            headers=auth_headers
        )
        
        assert response.status_code == 404
    
    @patch('app.services.firebase_service.firebase_service.send_notification')
    def test_send_test_notification(self, mock_send, client, auth_headers, test_db_session, test_user):
        """Test sending test notification"""
        # Mock successful FCM response
        mock_send.return_value = Mock(success=True, message_id="test_msg_id")
        
        # Create a device token
        device = DeviceToken(
            user_id=test_user.user_id,
            device_token="test_token",
            device_type="ios",
            is_active=True
        )
        test_db_session.add(device)
        test_db_session.commit()
        
        test_data = {
            "title": "Test Notification",
            "body": "This is a test notification"
        }
        
        response = client.post(
            "/api/v1/notifications/test",
            json=test_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        mock_send.assert_called_once()


class TestNotificationTemplateAPI:
    """Test notification template management API endpoints"""
    
    @pytest.fixture
    def client(self):
        """Test client"""
        return TestClient(app)
    
    @pytest.fixture
    def admin_headers(self, test_admin_token):
        """Admin authentication headers"""
        return {"Authorization": f"Bearer {test_admin_token}"}
    
    def test_get_notification_templates(self, client, admin_headers, test_db_session):
        """Test getting notification templates"""
        # Create a test template
        template = NotificationTemplate(
            template_key="api_test_template",
            template_name="API Test Template",
            category="marketing",
            notification_type="test",
            title_templates={"vi": "Test", "en": "Test"},
            body_templates={"vi": "Test body", "en": "Test body"},
            is_active=True
        )
        test_db_session.add(template)
        test_db_session.commit()
        
        response = client.get(
            "/api/v1/notifications/admin/templates",
            headers=admin_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert len(data["templates"]) >= 1
    
    def test_create_notification_template(self, client, admin_headers):
        """Test creating notification template"""
        template_data = {
            "template_key": "new_api_template",
            "template_name": "New API Template",
            "category": "transactional",
            "notification_type": "api_test",
            "title_templates": {
                "vi": "Tiêu đề mới",
                "en": "New Title"
            },
            "body_templates": {
                "vi": "Nội dung mới",
                "en": "New Content"
            },
            "priority": "normal",
            "is_active": True
        }
        
        response = client.post(
            "/api/v1/notifications/admin/templates",
            json=template_data,
            headers=admin_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "template_id" in data
    
    def test_create_template_duplicate_key(self, client, admin_headers, test_db_session):
        """Test creating template with duplicate key"""
        # Create existing template
        existing_template = NotificationTemplate(
            template_key="duplicate_api_key",
            template_name="Existing Template",
            category="marketing",
            notification_type="test",
            title_templates={"vi": "Test", "en": "Test"},
            body_templates={"vi": "Test", "en": "Test"},
            is_active=True
        )
        test_db_session.add(existing_template)
        test_db_session.commit()
        
        # Try to create duplicate
        template_data = {
            "template_key": "duplicate_api_key",  # Same key
            "template_name": "Duplicate Template",
            "category": "transactional",
            "notification_type": "test",
            "title_templates": {"vi": "Test", "en": "Test"},
            "body_templates": {"vi": "Test", "en": "Test"}
        }
        
        response = client.post(
            "/api/v1/notifications/admin/templates",
            json=template_data,
            headers=admin_headers
        )
        
        assert response.status_code == 400
        assert "already exists" in response.json()["detail"]
    
    @patch('app.services.notification_service.notification_service.send_batch_notification')
    def test_send_notification_by_template(self, mock_send, client, admin_headers, test_db_session, test_user):
        """Test sending notification using template"""
        # Mock successful sending
        mock_send.return_value = {
            "success": True,
            "total_users": 1,
            "success_users": 1,
            "results": [{"user_id": str(test_user.user_id), "result": {"success": True}}]
        }
        
        # Create template
        template = NotificationTemplate(
            template_key="send_test_template",
            template_name="Send Test Template",
            category="marketing",
            notification_type="test",
            title_templates={"vi": "Test {user_name}", "en": "Test {user_name}"},
            body_templates={"vi": "Hello {user_name}", "en": "Hello {user_name}"},
            is_active=True
        )
        test_db_session.add(template)
        test_db_session.commit()
        
        notification_data = {
            "template_key": "send_test_template",
            "user_ids": [str(test_user.user_id)],
            "variables": {"user_name": "Test User"},
            "language": "vi"
        }
        
        response = client.post(
            "/api/v1/notifications/admin/send",
            json=notification_data,
            headers=admin_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["sent_count"] == 1
        mock_send.assert_called_once()
    
    def test_seed_default_templates(self, client, admin_headers):
        """Test seeding default templates"""
        response = client.post(
            "/api/v1/notifications/admin/templates/seed",
            headers=admin_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True


class TestNotificationAnalyticsAPI:
    """Test notification analytics API endpoints"""
    
    @pytest.fixture
    def client(self):
        """Test client"""
        return TestClient(app)
    
    @pytest.fixture
    def admin_headers(self, test_admin_token):
        """Admin authentication headers"""
        return {"Authorization": f"Bearer {test_admin_token}"}
    
    @pytest.fixture
    def user_headers(self, test_user_token):
        """User authentication headers"""
        return {"Authorization": f"Bearer {test_user_token}"}
    
    def test_get_notification_analytics(self, client, admin_headers):
        """Test getting notification analytics overview"""
        response = client.get(
            "/api/v1/notifications/admin/analytics/overview?days=30",
            headers=admin_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "analytics" in data
        assert "total_notifications" in data["analytics"]
    
    def test_get_template_performance(self, client, admin_headers):
        """Test getting template performance metrics"""
        response = client.get(
            "/api/v1/notifications/admin/analytics/templates?days=7",
            headers=admin_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "templates" in data
    
    def test_get_daily_stats(self, client, admin_headers):
        """Test getting daily statistics"""
        response = client.get(
            "/api/v1/notifications/admin/analytics/daily?days=7",
            headers=admin_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "stats" in data
    
    def test_get_user_engagement(self, client, user_headers):
        """Test getting user engagement metrics"""
        response = client.get(
            "/api/v1/notifications/my-engagement?days=30",
            headers=user_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "engagement" in data
    
    def test_track_notification_click(self, client, user_headers, test_db_session, test_user):
        """Test tracking notification click"""
        from app.models.notification_log import NotificationLog
        from uuid import uuid4
        
        # Create a notification log
        log = NotificationLog(
            user_id=test_user.user_id,
            title="Test Notification",
            body="Test Body",
            notification_type="test",
            category="test",
            delivery_status="sent"
        )
        test_db_session.add(log)
        test_db_session.commit()
        test_db_session.refresh(log)
        
        click_data = {
            "log_id": str(log.log_id)
        }
        
        response = client.post(
            "/api/v1/notifications/click",
            json=click_data,
            headers=user_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
    
    def test_track_click_unauthorized(self, client, user_headers):
        """Test tracking click for unauthorized notification"""
        from uuid import uuid4
        
        click_data = {
            "log_id": str(uuid4())  # Non-existent log
        }
        
        response = client.post(
            "/api/v1/notifications/click",
            json=click_data,
            headers=user_headers
        )
        
        assert response.status_code == 404
