"""
Test Pydantic schemas.
"""

import pytest
import sys
import os
from datetime import date
from decimal import Decimal

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from pydantic import ValidationError
from app.schemas.user import UserCreate, UserUpdate
from app.schemas.user_profile import UserProfileCreate
from app.schemas.recipe import RecipeCreate
from app.schemas.ingredient import IngredientCreate


class TestUserSchemas:
    """Test User schemas."""
    
    def test_user_create_valid(self):
        """Test creating valid user data."""
        user_data = {
            "email": "<EMAIL>",
            "auth_provider": "email",
            "display_name": "Test User"
        }
        
        user = UserCreate(**user_data)
        assert user.email == "<EMAIL>"
        assert user.auth_provider == "email"
        assert user.display_name == "Test User"
    
    def test_user_create_invalid_email(self):
        """Test user creation with invalid email."""
        user_data = {
            "email": "invalid-email",
            "auth_provider": "email"
        }
        
        with pytest.raises(ValidationError):
            UserCreate(**user_data)
    
    def test_user_update_partial(self):
        """Test partial user update."""
        update_data = {
            "display_name": "Updated Name"
        }
        
        user_update = UserUpdate(**update_data)
        assert user_update.display_name == "Updated Name"
        assert user_update.email is None  # Should be None for partial update


class TestUserProfileSchemas:
    """Test UserProfile schemas."""
    
    def test_user_profile_create_valid(self):
        """Test creating valid user profile data."""
        from uuid import uuid4
        
        profile_data = {
            "user_id": uuid4(),
            "date_of_birth": date(1990, 1, 1),
            "gender": "nam",
            "weight_kg": 70.5,
            "height_cm": 175.0,
            "health_goals": ["giảm cân", "tăng cơ"],
            "food_allergies": ["hải sản"],
            "sweet_preference_level": 3,
            "cooking_skill_level": "cơ bản"
        }
        
        profile = UserProfileCreate(**profile_data)
        assert profile.date_of_birth == date(1990, 1, 1)
        assert profile.health_goals == ["giảm cân", "tăng cơ"]
        assert profile.sweet_preference_level == 3
    
    def test_preference_level_validation(self):
        """Test preference level validation (1-5)."""
        from uuid import uuid4
        
        # Valid preference level
        profile_data = {
            "user_id": uuid4(),
            "sweet_preference_level": 3
        }
        profile = UserProfileCreate(**profile_data)
        assert profile.sweet_preference_level == 3
        
        # Invalid preference level (too high)
        profile_data["sweet_preference_level"] = 6
        with pytest.raises(ValidationError):
            UserProfileCreate(**profile_data)
        
        # Invalid preference level (too low)
        profile_data["sweet_preference_level"] = 0
        with pytest.raises(ValidationError):
            UserProfileCreate(**profile_data)


class TestRecipeSchemas:
    """Test Recipe schemas."""
    
    def test_recipe_create_valid(self):
        """Test creating valid recipe data."""
        recipe_data = {
            "name": "Test Recipe",
            "instructions": "1. Step 1\n2. Step 2",
            "servings": 4,
            "prep_time_minutes": 15,
            "cook_time_minutes": 30,
            "calories_per_serving": 250,
            "protein_g_per_serving": Decimal("12.5")
        }
        
        recipe = RecipeCreate(**recipe_data)
        assert recipe.name == "Test Recipe"
        assert recipe.servings == 4
        assert recipe.protein_g_per_serving == Decimal("12.5")
    
    def test_recipe_create_minimal(self):
        """Test creating recipe with minimal required data."""
        recipe_data = {
            "name": "Simple Recipe",
            "instructions": "Just cook it"
        }
        
        recipe = RecipeCreate(**recipe_data)
        assert recipe.name == "Simple Recipe"
        assert recipe.servings == 1  # Default value
    
    def test_recipe_with_nutrition_data(self):
        """Test recipe with complex nutrition data."""
        recipe_data = {
            "name": "Nutritious Meal",
            "instructions": "Cook with care",
            "servings": 2,
            "total_dish_heart_health_indicators": {
                "cholesterol_mg": 50,
                "fiber_g": 10,
                "omega3_g": 2.5
            },
            "total_dish_vitamins": {
                "vitamin_c": "cao",
                "vitamin_d_mcg": 15
            }
        }
        
        recipe = RecipeCreate(**recipe_data)
        assert recipe.total_dish_heart_health_indicators["fiber_g"] == 10
        assert recipe.total_dish_vitamins["vitamin_c"] == "cao"


class TestIngredientSchemas:
    """Test Ingredient schemas."""
    
    def test_ingredient_create_valid(self):
        """Test creating valid ingredient data."""
        ingredient_data = {
            "name": "Tomato",
            "description": "Fresh red tomato",
            "default_unit": "piece"
        }
        
        ingredient = IngredientCreate(**ingredient_data)
        assert ingredient.name == "Tomato"
        assert ingredient.description == "Fresh red tomato"
        assert ingredient.default_unit == "piece"
    
    def test_ingredient_create_minimal(self):
        """Test creating ingredient with minimal data."""
        ingredient_data = {
            "name": "Salt"
        }
        
        ingredient = IngredientCreate(**ingredient_data)
        assert ingredient.name == "Salt"
        assert ingredient.description is None
        assert ingredient.default_unit is None
