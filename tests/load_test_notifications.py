"""
Load testing for notification system using Locust.

Usage:
    pip install locust
    locust -f tests/load_test_notifications.py --host=http://localhost:8000
"""

import json
import random
from uuid import uuid4
from locust import HttpUser, task, between


class NotificationUser(HttpUser):
    """Simulated user for notification load testing"""
    
    wait_time = between(1, 3)  # Wait 1-3 seconds between requests
    
    def on_start(self):
        """Setup user session"""
        self.auth_token = self.login()
        self.device_token_id = self.register_device()
    
    def login(self):
        """Simulate user login to get auth token"""
        # In real scenario, you'd implement actual login
        # For testing, return a mock token
        return "mock_auth_token_for_load_testing"
    
    def register_device(self):
        """Register a device token for this user"""
        device_data = {
            "device_token": f"load_test_token_{uuid4()}",
            "device_type": random.choice(["ios", "android", "web"]),
            "device_id": f"load_test_device_{uuid4()}",
            "app_version": "1.0.0",
            "os_version": random.choice(["16.0", "13.0", "14.0"])
        }
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        with self.client.post(
            "/api/v1/notifications/devices/register",
            json=device_data,
            headers=headers,
            catch_response=True
        ) as response:
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    return data.get("token_id")
                else:
                    response.failure("Device registration failed")
            else:
                response.failure(f"Device registration failed with status {response.status_code}")
        
        return None
    
    @task(3)
    def get_user_devices(self):
        """Get user's registered devices"""
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        with self.client.get(
            "/api/v1/notifications/devices",
            headers=headers,
            catch_response=True
        ) as response:
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    response.success()
                else:
                    response.failure("Failed to get devices")
            else:
                response.failure(f"Get devices failed with status {response.status_code}")
    
    @task(2)
    def send_test_notification(self):
        """Send test notification"""
        if not self.device_token_id:
            return
        
        test_data = {
            "title": f"Load Test Notification {random.randint(1, 1000)}",
            "body": "This is a load test notification",
            "device_token_id": self.device_token_id
        }
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        with self.client.post(
            "/api/v1/notifications/test",
            json=test_data,
            headers=headers,
            catch_response=True
        ) as response:
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    response.success()
                else:
                    response.failure("Test notification failed")
            else:
                response.failure(f"Test notification failed with status {response.status_code}")
    
    @task(1)
    def get_user_engagement(self):
        """Get user engagement metrics"""
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        with self.client.get(
            "/api/v1/notifications/my-engagement?days=7",
            headers=headers,
            catch_response=True
        ) as response:
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    response.success()
                else:
                    response.failure("Failed to get engagement")
            else:
                response.failure(f"Get engagement failed with status {response.status_code}")


class AdminUser(HttpUser):
    """Simulated admin user for admin operations"""
    
    wait_time = between(2, 5)  # Admins are less frequent
    weight = 1  # Lower weight than regular users
    
    def on_start(self):
        """Setup admin session"""
        self.admin_token = "mock_admin_token_for_load_testing"
    
    @task(2)
    def get_notification_templates(self):
        """Get notification templates"""
        headers = {"Authorization": f"Bearer {self.admin_token}"}
        
        with self.client.get(
            "/api/v1/notifications/admin/templates",
            headers=headers,
            catch_response=True
        ) as response:
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    response.success()
                else:
                    response.failure("Failed to get templates")
            else:
                response.failure(f"Get templates failed with status {response.status_code}")
    
    @task(1)
    def get_analytics_overview(self):
        """Get analytics overview"""
        headers = {"Authorization": f"Bearer {self.admin_token}"}
        
        with self.client.get(
            "/api/v1/notifications/admin/analytics/overview?days=7",
            headers=headers,
            catch_response=True
        ) as response:
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    response.success()
                else:
                    response.failure("Failed to get analytics")
            else:
                response.failure(f"Get analytics failed with status {response.status_code}")
    
    @task(1)
    def get_template_performance(self):
        """Get template performance metrics"""
        headers = {"Authorization": f"Bearer {self.admin_token}"}
        
        with self.client.get(
            "/api/v1/notifications/admin/analytics/templates?days=7",
            headers=headers,
            catch_response=True
        ) as response:
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    response.success()
                else:
                    response.failure("Failed to get template performance")
            else:
                response.failure(f"Get template performance failed with status {response.status_code}")
    
    @task(1)
    def send_batch_notification(self):
        """Send batch notification (simulated)"""
        # Generate mock user IDs for testing
        user_ids = [str(uuid4()) for _ in range(random.randint(1, 5))]
        
        notification_data = {
            "template_key": "recipe_suggestion",  # Assume this template exists
            "user_ids": user_ids,
            "variables": {
                "recipe_name": f"Load Test Recipe {random.randint(1, 100)}",
                "recipe_id": str(uuid4())
            },
            "language": "vi"
        }
        
        headers = {"Authorization": f"Bearer {self.admin_token}"}
        
        with self.client.post(
            "/api/v1/notifications/admin/send",
            json=notification_data,
            headers=headers,
            catch_response=True
        ) as response:
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    response.success()
                else:
                    response.failure("Batch notification failed")
            else:
                response.failure(f"Batch notification failed with status {response.status_code}")


class HighVolumeUser(HttpUser):
    """High volume user for stress testing"""
    
    wait_time = between(0.1, 0.5)  # Very fast requests
    weight = 5  # Higher weight for stress testing
    
    def on_start(self):
        """Setup high volume user"""
        self.auth_token = "mock_high_volume_token"
    
    @task(10)
    def rapid_device_registration(self):
        """Rapidly register device tokens"""
        device_data = {
            "device_token": f"high_volume_token_{uuid4()}",
            "device_type": random.choice(["ios", "android", "web"]),
            "device_id": f"high_volume_device_{uuid4()}"
        }
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        with self.client.post(
            "/api/v1/notifications/devices/register",
            json=device_data,
            headers=headers,
            catch_response=True
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"High volume registration failed: {response.status_code}")
    
    @task(5)
    def rapid_test_notifications(self):
        """Rapidly send test notifications"""
        test_data = {
            "title": f"High Volume Test {random.randint(1, 10000)}",
            "body": "High volume test notification"
        }
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        with self.client.post(
            "/api/v1/notifications/test",
            json=test_data,
            headers=headers,
            catch_response=True
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"High volume test failed: {response.status_code}")


# Custom load test scenarios
class NotificationLoadTest:
    """Custom load test scenarios"""
    
    @staticmethod
    def simulate_user_onboarding_burst():
        """Simulate burst of new user registrations"""
        # This would be implemented as a custom Locust test
        pass
    
    @staticmethod
    def simulate_daily_notification_campaign():
        """Simulate daily notification campaign"""
        # This would send notifications to many users simultaneously
        pass
    
    @staticmethod
    def simulate_database_stress():
        """Simulate database stress with many concurrent operations"""
        # This would test database performance under load
        pass


# Load test configuration
if __name__ == "__main__":
    print("Load test configuration:")
    print("- NotificationUser: Regular user operations (weight: default)")
    print("- AdminUser: Admin operations (weight: 1)")
    print("- HighVolumeUser: Stress testing (weight: 5)")
    print("")
    print("Run with: locust -f load_test_notifications.py --host=http://localhost:8000")
    print("Then open http://localhost:8089 to configure and start the test")
