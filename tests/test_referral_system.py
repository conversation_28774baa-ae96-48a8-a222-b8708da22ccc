"""
Comprehensive tests for Referral System
"""

import pytest
import sys
import os
from datetime import datetime, timed<PERSON>ta
from decimal import Decimal
from uuid import uuid4

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from app.models.user import User
from app.models.referral_voucher import ReferralVoucher
from app.models.referral_history import ReferralHistory
from app.services.referral_service import referral_service
from app.crud.crud_referral import referral_voucher, referral_history, referral_user
from app.schemas.referral import (
    ReferralVoucherCreate,
    ReferralHistoryCreate,
    ApplyReferralCodeRequest
)


class TestReferralService:
    """Test ReferralService functionality"""
    
    def test_normalize_vietnamese_name(self):
        """Test Vietnamese name normalization"""
        # Test Vietnamese characters
        assert referral_service.normalize_vietnamese_name("Nguyễn Văn <PERSON>") == "NGUYEN"
        assert referral_service.normalize_vietnamese_name("Trần <PERSON> B<PERSON>nh") == "TRANTHI"
        assert referral_service.normalize_vietnamese_name("<PERSON>ê <PERSON> Đứ<PERSON>") == "LEHOAN"
        
        # Test edge cases
        assert referral_service.normalize_vietnamese_name("") == "USER"
        assert referral_service.normalize_vietnamese_name(None) == "USER"
        assert referral_service.normalize_vietnamese_name("123456789") == "123456"
        assert referral_service.normalize_vietnamese_name("A") == "A"
    
    def test_generate_referral_code(self, clean_db):
        """Test referral code generation"""
        # Test normal case
        code1 = referral_service.generate_referral_code(clean_db, "Nguyễn An")
        assert code1.startswith("BANACHEF-NGUYEN")
        assert len(code1) == len("BANACHEF-NGUYEN") + 3  # 3 digit number
        
        # Test uniqueness
        code2 = referral_service.generate_referral_code(clean_db, "Nguyễn An")
        assert code1 != code2
        
        # Create user with first code to test collision handling
        user1 = User(
            email="<EMAIL>",
            auth_provider="email",
            referral_code=code1
        )
        clean_db.add(user1)
        clean_db.commit()
        
        # Generate another code - should be different
        code3 = referral_service.generate_referral_code(clean_db, "Nguyễn An")
        assert code3 != code1


class TestReferralCRUD:
    """Test CRUD operations for referral system"""
    
    def test_create_referral_voucher(self, clean_db, sample_users):
        """Test creating referral voucher"""
        user1, user2 = sample_users
        
        # Create referral history first
        history = referral_history.create(clean_db, obj_in=ReferralHistoryCreate(
            referrer_user_id=user1.user_id,
            referred_user_id=user2.user_id,
            status="completed"
        ))
        
        # Create voucher
        voucher = referral_voucher.create(clean_db, obj_in=ReferralVoucherCreate(
            owner_user_id=user1.user_id,
            source_referral_id=history.id,
            amount_usd=Decimal("5.00"),
            status="available"
        ))
        
        assert voucher.voucher_id is not None
        assert voucher.owner_user_id == user1.user_id
        assert voucher.amount_usd == Decimal("5.00")
        assert voucher.status == "available"
        assert voucher.expires_at is not None
    
    def test_get_available_vouchers(self, clean_db, sample_users):
        """Test getting available vouchers"""
        user1, user2 = sample_users
        
        # Create referral history
        history = referral_history.create(clean_db, obj_in=ReferralHistoryCreate(
            referrer_user_id=user1.user_id,
            referred_user_id=user2.user_id,
            status="completed"
        ))
        
        # Create available voucher
        voucher1 = referral_voucher.create(clean_db, obj_in=ReferralVoucherCreate(
            owner_user_id=user1.user_id,
            source_referral_id=history.id,
            amount_usd=Decimal("5.00"),
            status="available"
        ))
        
        # Create used voucher
        voucher2 = referral_voucher.create(clean_db, obj_in=ReferralVoucherCreate(
            owner_user_id=user1.user_id,
            source_referral_id=history.id,
            amount_usd=Decimal("5.00"),
            status="used"
        ))
        
        # Get available vouchers
        available = referral_voucher.get_available_vouchers(clean_db, user1.user_id)
        assert len(available) == 1
        assert available[0].voucher_id == voucher1.voucher_id
    
    def test_mark_voucher_as_used(self, clean_db, sample_users):
        """Test marking voucher as used"""
        user1, user2 = sample_users
        
        # Create referral history
        history = referral_history.create(clean_db, obj_in=ReferralHistoryCreate(
            referrer_user_id=user1.user_id,
            referred_user_id=user2.user_id,
            status="completed"
        ))
        
        # Create voucher
        voucher = referral_voucher.create(clean_db, obj_in=ReferralVoucherCreate(
            owner_user_id=user1.user_id,
            source_referral_id=history.id,
            amount_usd=Decimal("5.00"),
            status="available"
        ))
        
        # Mark as used
        used_voucher = referral_voucher.mark_as_used(clean_db, voucher.voucher_id)
        
        assert used_voucher.status == "used"
        assert used_voucher.used_at is not None


class TestReferralBusinessLogic:
    """Test business logic for referral system"""
    
    def test_apply_referral_code_success(self, clean_db):
        """Test successful referral code application"""
        # Create referrer
        referrer = User(
            email="<EMAIL>",
            auth_provider="email",
            display_name="Referrer User",
            referral_code="BANACHEF-REFER123"
        )
        clean_db.add(referrer)
        clean_db.flush()
        
        # Create new user
        new_user = User(
            email="<EMAIL>",
            auth_provider="email",
            display_name="New User",
            has_made_first_purchase=False
        )
        clean_db.add(new_user)
        clean_db.flush()
        
        # Apply referral code
        result = referral_service.apply_referral_code(
            clean_db, new_user.user_id, "BANACHEF-REFER123"
        )
        
        assert result["success"] is True
        assert "Referrer User" in result["message"]
        
        # Verify user was updated
        clean_db.refresh(new_user)
        assert new_user.referred_by_user_id == referrer.user_id
        
        # Verify referral history was created
        history = referral_history.get_by_referred_user(clean_db, new_user.user_id)
        assert history is not None
        assert history.referrer_user_id == referrer.user_id
        assert history.status == "pending"
    
    def test_apply_referral_code_already_applied(self, clean_db, sample_users):
        """Test applying referral code when already applied"""
        user1, user2 = sample_users
        
        # Set user2 as already referred by user1
        user2.referred_by_user_id = user1.user_id
        clean_db.add(user2)
        clean_db.commit()
        
        # Try to apply another code
        result = referral_service.apply_referral_code(
            clean_db, user2.user_id, "BANACHEF-OTHER123"
        )
        
        assert result["success"] is False
        assert "already applied" in result["message"]
    
    def test_apply_referral_code_after_purchase(self, clean_db):
        """Test applying referral code after first purchase"""
        # Create user who already made purchase
        user = User(
            email="<EMAIL>",
            auth_provider="email",
            has_made_first_purchase=True
        )
        clean_db.add(user)
        clean_db.flush()
        
        result = referral_service.apply_referral_code(
            clean_db, user.user_id, "BANACHEF-TEST123"
        )
        
        assert result["success"] is False
        assert "before making your first purchase" in result["message"]
    
    def test_self_referral_prevention(self, clean_db):
        """Test prevention of self-referral"""
        # Create user
        user = User(
            email="<EMAIL>",
            auth_provider="email",
            referral_code="BANACHEF-SELF123"
        )
        clean_db.add(user)
        clean_db.flush()
        
        # Try to apply own code
        result = referral_service.apply_referral_code(
            clean_db, user.user_id, "BANACHEF-SELF123"
        )
        
        assert result["success"] is False
        assert "cannot refer yourself" in result["message"]
    
    def test_process_first_purchase_with_referrer(self, clean_db, sample_users):
        """Test processing first purchase with referrer reward"""
        user1, user2 = sample_users
        
        # Set up referral relationship
        user2.referred_by_user_id = user1.user_id
        clean_db.add(user2)
        
        # Create pending referral history
        history = referral_history.create(clean_db, obj_in=ReferralHistoryCreate(
            referrer_user_id=user1.user_id,
            referred_user_id=user2.user_id,
            status="pending"
        ))
        
        # Process first purchase
        result = referral_service.process_first_purchase(
            clean_db, user2.user_id, applied_vouchers=[]
        )
        
        assert result["success"] is True
        assert result.get("referrer_rewarded") is True
        
        # Verify user marked as purchased
        clean_db.refresh(user2)
        assert user2.has_made_first_purchase is True
        
        # Verify referral history marked as completed
        clean_db.refresh(history)
        assert history.status == "completed"
        
        # Verify voucher created for referrer
        vouchers = referral_voucher.get_available_vouchers(clean_db, user1.user_id)
        assert len(vouchers) == 1
        assert vouchers[0].amount_usd == Decimal("5.00")


class TestReferralAPIIntegration:
    """Integration tests for referral API endpoints"""

    def test_get_purchase_offerings_new_user_with_referrer(self, clean_db, sample_users):
        """Test purchase offerings for new user with referrer"""
        user1, user2 = sample_users

        # Set up referral relationship
        user2.referred_by_user_id = user1.user_id
        clean_db.add(user2)
        clean_db.commit()

        # Mock the purchase offerings logic
        total_discount = Decimal("0.00")
        applied_vouchers = []
        new_user_discount_applied = False

        # Check for new user discount
        if not user2.has_made_first_purchase and user2.referred_by_user_id:
            total_discount += Decimal("5.00")
            new_user_discount_applied = True

        # Check for referral voucher discount
        available_voucher = referral_voucher.get_first_available_voucher(clean_db, user2.user_id)
        if available_voucher:
            total_discount += available_voucher.amount_usd
            applied_vouchers.append(available_voucher.voucher_id)

        assert total_discount == Decimal("5.00")
        assert new_user_discount_applied is True
        assert len(applied_vouchers) == 0  # No vouchers for new user

    def test_get_purchase_offerings_referrer_with_vouchers(self, clean_db, sample_users):
        """Test purchase offerings for referrer with available vouchers"""
        user1, user2 = sample_users

        # Create completed referral history
        history = referral_history.create(clean_db, obj_in=ReferralHistoryCreate(
            referrer_user_id=user1.user_id,
            referred_user_id=user2.user_id,
            status="completed"
        ))

        # Create available voucher for user1
        voucher = referral_voucher.create(clean_db, obj_in=ReferralVoucherCreate(
            owner_user_id=user1.user_id,
            source_referral_id=history.id,
            amount_usd=Decimal("5.00"),
            status="available"
        ))

        # Mock the purchase offerings logic for user1
        total_discount = Decimal("0.00")
        applied_vouchers = []
        new_user_discount_applied = False

        # Check for new user discount (user1 has no referrer)
        if not user1.has_made_first_purchase and user1.referred_by_user_id:
            total_discount += Decimal("5.00")
            new_user_discount_applied = True

        # Check for referral voucher discount
        available_voucher = referral_voucher.get_first_available_voucher(clean_db, user1.user_id)
        if available_voucher:
            total_discount += available_voucher.amount_usd
            applied_vouchers.append(available_voucher.voucher_id)

        assert total_discount == Decimal("5.00")
        assert new_user_discount_applied is False
        assert len(applied_vouchers) == 1
        assert applied_vouchers[0] == voucher.voucher_id

    def test_complete_referral_flow(self, clean_db):
        """Test complete referral flow from code application to purchase"""
        # Step 1: Create referrer
        referrer = User(
            email="<EMAIL>",
            auth_provider="email",
            display_name="Referrer",
            referral_code="BANACHEF-REFER123",
            has_made_first_purchase=True  # Already a customer
        )
        clean_db.add(referrer)
        clean_db.flush()

        # Step 2: Create new user
        new_user = User(
            email="<EMAIL>",
            auth_provider="email",
            display_name="New User",
            has_made_first_purchase=False
        )
        clean_db.add(new_user)
        clean_db.flush()

        # Step 3: Apply referral code
        result = referral_service.apply_referral_code(
            clean_db, new_user.user_id, "BANACHEF-REFER123"
        )
        assert result["success"] is True

        # Step 4: Check purchase offerings for new user
        clean_db.refresh(new_user)
        total_discount = Decimal("0.00")
        if not new_user.has_made_first_purchase and new_user.referred_by_user_id:
            total_discount += Decimal("5.00")

        assert total_discount == Decimal("5.00")

        # Step 5: Process first purchase
        purchase_result = referral_service.process_first_purchase(
            clean_db, new_user.user_id, applied_vouchers=[]
        )
        assert purchase_result["success"] is True
        assert purchase_result.get("referrer_rewarded") is True

        # Step 6: Verify referrer got voucher
        vouchers = referral_voucher.get_available_vouchers(clean_db, referrer.user_id)
        assert len(vouchers) == 1
        assert vouchers[0].amount_usd == Decimal("5.00")

        # Step 7: Verify new user can't apply another code
        another_result = referral_service.apply_referral_code(
            clean_db, new_user.user_id, "BANACHEF-OTHER123"
        )
        assert another_result["success"] is False


@pytest.fixture
def sample_users(clean_db):
    """Create sample users for testing"""
    user1 = User(
        email="<EMAIL>",
        auth_provider="email",
        display_name="User One",
        referral_code="BANACHEF-USER1123"
    )
    user2 = User(
        email="<EMAIL>",
        auth_provider="email",
        display_name="User Two",
        has_made_first_purchase=False
    )

    clean_db.add(user1)
    clean_db.add(user2)
    clean_db.commit()

    return user1, user2
