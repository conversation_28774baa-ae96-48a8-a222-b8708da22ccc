#!/usr/bin/env python3
"""
Test script to verify all imports work correctly.
"""

import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))


def test_config_import():
    """Test config import."""
    from app.core.config import settings
    assert settings.APP_NAME is not None
    assert settings.DATABASE_URL is not None


def test_database_session_import():
    """Test database session import."""
    from app.db.session import Base, engine, SessionLocal
    assert Base is not None
    assert engine is not None
    assert SessionLocal is not None


def test_models_import():
    """Test all models import."""
    from app.models.user import User
    from app.models.user_profile import UserProfile
    from app.models.user_temporary_state import UserTemporaryState
    from app.models.recipe import Recipe
    from app.models.ingredient import Ingredient
    from app.models.recipe_ingredient import RecipeIngredient
    
    # Check table names
    assert User.__tablename__ == "users"
    assert UserProfile.__tablename__ == "user_profiles"
    assert UserTemporaryState.__tablename__ == "user_temporary_states"
    assert Recipe.__tablename__ == "recipes"
    assert Ingredient.__tablename__ == "ingredients"
    assert RecipeIngredient.__tablename__ == "recipe_ingredients"


def test_schemas_import():
    """Test all schemas import."""
    from app.schemas.user import UserCreate, UserUpdate
    from app.schemas.user_profile import UserProfileCreate
    from app.schemas.recipe import RecipeCreate
    from app.schemas.ingredient import IngredientCreate
    
    # Basic validation that classes exist
    assert UserCreate is not None
    assert UserUpdate is not None
    assert UserProfileCreate is not None
    assert RecipeCreate is not None
    assert IngredientCreate is not None


if __name__ == "__main__":
    print("Testing imports...")
    
    try:
        test_config_import()
        print("✅ Config imported successfully")
        
        test_database_session_import()
        print("✅ Database session imported successfully")
        
        test_models_import()
        print("✅ All models imported successfully")
        
        test_schemas_import()
        print("✅ All schemas imported successfully")
        
        print("\n🎉 All imports successful!")
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
