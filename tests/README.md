# Tests for Bana Chef Server

Thư mục này chứa tất cả các test cho Bana Chef Server.

## Cấu trúc Tests

```
tests/
├── __init__.py                    # Package init
├── conftest.py                    # Pytest configuration và fixtures
├── pytest.ini                    # Pytest settings
├── README.md                      # Documentation này
├── test_database.py               # Unit tests cho database operations
├── test_database_integration.py   # Integration tests với PostgreSQL
├── test_imports.py                # Tests cho imports
├── test_models.py                 # Unit tests cho SQLAlchemy models
└── test_schemas.py                # Unit tests cho Pydantic schemas
```

## Loại Tests

### 1. **Import Tests** (`test_imports.py`)
- <PERSON><PERSON><PERSON> tra tất cả imports hoạt động đúng
- Test config, database session, models, schemas
- Chạy độc lập không cần database

### 2. **Unit Tests** (pytest)
- **`test_models.py`**: Test SQLAlchemy models với in-memory SQLite
- **`test_schemas.py`**: Test Pydantic schemas validation
- **`test_database.py`**: Test database operations cơ bản

### 3. **Integration Tests** (`test_database_integration.py`)
- Test với PostgreSQL thật
- Test tạo dữ liệu mẫu
- Test relationships giữa models
- Test queries phức tạp

## Chạy Tests

### Chạy tất cả tests:
```bash
python run_tests.py
```

### Chạy từng loại test:

#### Import tests:
```bash
python tests/test_imports.py
```

#### Integration tests:
```bash
python tests/test_database_integration.py
```

#### Pytest (cần cài pytest):
```bash
pip install pytest pytest-asyncio
pytest tests/ -v
```

### Chạy test cụ thể:
```bash
pytest tests/test_models.py::TestUserModel::test_create_user -v
```

## Fixtures

### Database Fixtures (conftest.py):
- `test_engine`: SQLite in-memory engine cho tests
- `test_db_session`: Database session cho mỗi test
- `clean_db`: Database session với cleanup sau mỗi test

### Data Fixtures:
- `sample_user_data`: Dữ liệu user mẫu
- `sample_ingredient_data`: Dữ liệu ingredient mẫu  
- `sample_recipe_data`: Dữ liệu recipe mẫu

## Yêu cầu

### Cho Unit Tests:
- SQLite (built-in Python)
- pytest (optional)

### Cho Integration Tests:
- PostgreSQL running
- Database connection configured

## Cài đặt Dependencies

```bash
# Cài đặt pytest (optional)
pip install pytest pytest-asyncio

# Hoặc cài tất cả dependencies
pip install -r requirements.txt
```

## Chạy Tests trong CI/CD

```bash
# Chạy chỉ unit tests (không cần PostgreSQL)
pytest tests/test_models.py tests/test_schemas.py -v

# Chạy integration tests (cần PostgreSQL)
python tests/test_database_integration.py
```

## Thêm Tests Mới

### 1. Unit Test mới:
```python
# tests/test_new_feature.py
import pytest
from models.new_model import NewModel

class TestNewFeature:
    def test_new_functionality(self, clean_db):
        # Test code here
        pass
```

### 2. Integration Test mới:
```python
# tests/test_new_integration.py
def test_new_integration():
    # Integration test code
    pass
```

### 3. Fixture mới:
```python
# tests/conftest.py
@pytest.fixture
def new_fixture():
    return {"test": "data"}
```

## Best Practices

1. **Sử dụng fixtures** cho dữ liệu test
2. **Clean database** sau mỗi test
3. **Test cả success và error cases**
4. **Sử dụng descriptive test names**
5. **Group related tests** trong classes
6. **Mock external dependencies** khi cần thiết

## Troubleshooting

### Import errors:
- Kiểm tra PYTHONPATH
- Chạy từ root directory của project

### Database connection errors:
- Kiểm tra PostgreSQL đang chạy
- Kiểm tra credentials trong .env

### Pytest not found:
```bash
pip install pytest pytest-asyncio
```
