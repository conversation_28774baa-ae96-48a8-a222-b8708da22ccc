#!/usr/bin/env python3
"""
Integration test for database operations with real PostgreSQL.
"""

import sys
import os
from datetime import datetime, date
from decimal import Decimal

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from sqlalchemy.orm import Session
from sqlalchemy import text
from app.db.session import SessionLocal
from app.core.config import settings
from app.models.user import User
from app.models.user_profile import UserProfile
from app.models.ingredient import Ingredient
from app.models.recipe import Recipe


def test_database_connection():
    """Test database connection."""
    print("🧪 Testing database connection...")
    print(f"📊 Database URL: {settings.DATABASE_URL}")
    
    db = SessionLocal()
    try:
        result = db.execute(text("SELECT 1"))
        assert result.scalar() == 1
        print("✅ Database connection successful")
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False
    finally:
        db.close()


def test_create_sample_data():
    """Test creating sample data."""
    print("🧪 Testing sample data creation...")
    
    db = SessionLocal()
    try:
        # Create a user with unique email
        import uuid
        unique_email = f"test_{uuid.uuid4().hex[:8]}@example.com"
        
        user = User(
            email=unique_email,
            auth_provider="email",
            display_name="Test User"
        )
        db.add(user)
        db.flush()  # Get the user_id
        
        # Create user profile
        profile = UserProfile(
            user_id=user.user_id,
            date_of_birth=date(1990, 1, 1),
            gender="nam",
            weight_kg=Decimal("70.5"),
            height_cm=Decimal("175.0"),
            activity_level="vận động nhẹ",
            health_goals=["giảm cân", "tăng cơ"],
            food_allergies=["hải sản"],
            sweet_preference_level=3,
            salty_preference_level=4,
            cooking_skill_level="cơ bản",
            preferred_cuisines=["Việt Nam", "Nhật Bản"]
        )
        db.add(profile)
        
        # Create ingredient with unique name
        unique_ingredient_name = f"Test Ingredient {uuid.uuid4().hex[:8]}"
        ingredient = Ingredient(
            name=unique_ingredient_name,
            description="A test ingredient",
            default_unit="kg"
        )
        db.add(ingredient)
        db.flush()
        
        # Create recipe
        recipe = Recipe(
            name=f"Test Recipe {uuid.uuid4().hex[:8]}",
            description="A test recipe",
            instructions="1. Test step 1\n2. Test step 2",
            prep_time_minutes=10,
            cook_time_minutes=20,
            servings=4,
            calories_per_serving=200,
            protein_g_per_serving=Decimal("15.0"),
            carbs_g_per_serving=Decimal("30.0"),
            fat_g_per_serving=Decimal("8.0"),
            source="test_created"
        )
        db.add(recipe)
        
        db.commit()
        print("✅ Sample data created successfully")
        
        # Test relationships
        db.refresh(user)
        print(f"📊 User: {user.email}")
        print(f"📊 Profile: {user.profile.cooking_skill_level if user.profile else 'None'}")
        print(f"📊 Recipe: {recipe.name}")
        print(f"📊 Ingredient: {ingredient.name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating sample data: {e}")
        import traceback
        traceback.print_exc()
        db.rollback()
        return False
    finally:
        db.close()


def test_query_data():
    """Test querying data."""
    print("🧪 Testing data queries...")
    
    db = SessionLocal()
    try:
        # Query counts
        user_count = db.query(User).count()
        ingredient_count = db.query(Ingredient).count()
        recipe_count = db.query(Recipe).count()
        
        print(f"📊 Total users: {user_count}")
        print(f"📊 Total ingredients: {ingredient_count}")
        print(f"📊 Total recipes: {recipe_count}")
        
        # Query with relationships
        users_with_profiles = db.query(User).join(UserProfile).count()
        print(f"📊 Users with profiles: {users_with_profiles}")
        
        print("✅ Data queries successful")
        return True
        
    except Exception as e:
        print(f"❌ Error querying data: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()


def main():
    """Run all integration tests."""
    print("🚀 Starting database integration tests...")
    
    tests = [
        test_database_connection,
        test_create_sample_data,
        test_query_data
    ]
    
    passed = 0
    for test in tests:
        try:
            if test():
                passed += 1
            print()  # Add spacing between tests
        except Exception as e:
            print(f"❌ Test {test.__name__} failed: {e}")
    
    print(f"📊 Tests passed: {passed}/{len(tests)}")
    
    if passed == len(tests):
        print("🎉 All integration tests passed!")
        return True
    else:
        print("❌ Some tests failed!")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
