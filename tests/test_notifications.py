"""
Tests for push notification system.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from uuid import uuid4
from datetime import datetime

from app.services.notification_service import notification_service
from app.services.firebase_service import firebase_service, NotificationPayload
from app.models.user import User
from app.models.device_token import DeviceToken
from app.models.notification_template import NotificationTemplate
from app.models.notification_log import NotificationLog
from app.schemas.notification_template import NotificationTemplateCreate
from app.schemas.device_token import DeviceTokenRegister


class TestFirebaseService:
    """Test Firebase service functionality"""
    
    @pytest.fixture
    def mock_firebase_app(self):
        """Mock Firebase app initialization"""
        with patch('app.services.firebase_service.firebase_admin') as mock_admin:
            mock_admin._apps = []
            mock_admin.initialize_app.return_value = Mock()
            yield mock_admin
    
    @pytest.fixture
    def notification_payload(self):
        """Sample notification payload"""
        return NotificationPayload(
            title="Test Notification",
            body="This is a test notification",
            image_url="https://example.com/image.jpg",
            action_url="banachef://test",
            data={"test": "true"},
            priority="normal"
        )
    
    def test_firebase_initialization(self, mock_firebase_app):
        """Test Firebase service initialization"""
        from app.services.firebase_service import FirebaseService
        
        service = FirebaseService()
        assert service._app is not None
        mock_firebase_app.initialize_app.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_send_notification_success(self, mock_firebase_app, notification_payload):
        """Test successful notification sending"""
        with patch('app.services.firebase_service.messaging') as mock_messaging:
            mock_messaging.send.return_value = "message_id_123"
            
            result = await firebase_service.send_notification(
                "test_device_token",
                notification_payload
            )
            
            assert result.success is True
            assert result.message_id == "message_id_123"
            assert result.success_count == 1
            mock_messaging.send.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_send_notification_unregistered_token(self, mock_firebase_app, notification_payload):
        """Test handling of unregistered device token"""
        with patch('app.services.firebase_service.messaging') as mock_messaging:
            from firebase_admin import messaging
            mock_messaging.send.side_effect = messaging.UnregisteredError("Token unregistered")
            
            result = await firebase_service.send_notification(
                "invalid_token",
                notification_payload
            )
            
            assert result.success is False
            assert result.error == "unregistered_token"
            assert result.failure_count == 1
    
    @pytest.mark.asyncio
    async def test_send_batch_notifications(self, mock_firebase_app, notification_payload):
        """Test batch notification sending"""
        with patch('app.services.firebase_service.messaging') as mock_messaging:
            # Mock batch response
            mock_response = Mock()
            mock_response.success_count = 2
            mock_response.failure_count = 0
            mock_response.responses = [Mock(success=True), Mock(success=True)]
            mock_messaging.send_all.return_value = mock_response
            
            device_tokens = ["token1", "token2"]
            result = await firebase_service.send_batch_notifications(
                device_tokens,
                notification_payload
            )
            
            assert result.success is True
            assert result.success_count == 2
            assert result.failure_count == 0


class TestNotificationService:
    """Test notification service functionality"""
    
    @pytest.fixture
    def sample_user(self, test_db_session):
        """Create sample user for testing"""
        user = User(
            email="<EMAIL>",
            display_name="Test User",
            auth_provider="google",
            provider_user_id="google_123"
        )
        test_db_session.add(user)
        test_db_session.commit()
        test_db_session.refresh(user)
        return user
    
    @pytest.fixture
    def sample_device_token(self, test_db_session, sample_user):
        """Create sample device token"""
        device = DeviceToken(
            user_id=sample_user.user_id,
            device_token="test_fcm_token",
            device_type="ios",
            device_id="test_device",
            is_active=True
        )
        test_db_session.add(device)
        test_db_session.commit()
        test_db_session.refresh(device)
        return device
    
    @pytest.fixture
    def sample_template(self, test_db_session):
        """Create sample notification template"""
        template = NotificationTemplate(
            template_key="test_template",
            template_name="Test Template",
            category="transactional",
            notification_type="test",
            title_templates={"vi": "Test {user_name}", "en": "Test {user_name}"},
            body_templates={"vi": "Hello {user_name}!", "en": "Hello {user_name}!"},
            priority="normal",
            is_active=True
        )
        test_db_session.add(template)
        test_db_session.commit()
        test_db_session.refresh(template)
        return template
    
    @pytest.mark.asyncio
    async def test_send_notification_by_template(
        self, 
        test_db_session, 
        sample_user, 
        sample_device_token, 
        sample_template
    ):
        """Test sending notification using template"""
        with patch.object(firebase_service, 'send_notification') as mock_send:
            mock_send.return_value = Mock(success=True, message_id="test_msg_id")
            
            result = await notification_service.send_notification_by_template(
                db=test_db_session,
                user_id=sample_user.user_id,
                template_key="test_template",
                variables={"user_name": "Test User"}
            )
            
            assert result["success"] is True
            assert result["sent_count"] == 1
            assert result["total_count"] == 1
            
            # Check notification log was created
            log = test_db_session.query(NotificationLog).first()
            assert log is not None
            assert log.user_id == sample_user.user_id
            assert log.delivery_status == "sent"
    
    @pytest.mark.asyncio
    async def test_send_notification_no_devices(self, test_db_session, sample_user, sample_template):
        """Test sending notification to user with no devices"""
        result = await notification_service.send_notification_by_template(
            db=test_db_session,
            user_id=sample_user.user_id,
            template_key="test_template",
            variables={"user_name": "Test User"}
        )
        
        assert result["success"] is False
        assert result["error"] == "No active devices"
    
    @pytest.mark.asyncio
    async def test_send_notification_template_not_found(
        self, 
        test_db_session, 
        sample_user, 
        sample_device_token
    ):
        """Test sending notification with non-existent template"""
        result = await notification_service.send_notification_by_template(
            db=test_db_session,
            user_id=sample_user.user_id,
            template_key="non_existent_template",
            variables={"user_name": "Test User"}
        )
        
        assert result["success"] is False
        assert "not found" in result["error"]
    
    def test_render_template(self, sample_template):
        """Test template rendering with variables"""
        rendered = notification_service._render_template(
            template=sample_template,
            variables={"user_name": "John Doe"},
            language="vi"
        )
        
        assert rendered["title"] == "Test John Doe"
        assert rendered["body"] == "Hello John Doe!"
    
    def test_render_template_fallback_language(self, sample_template):
        """Test template rendering with fallback language"""
        rendered = notification_service._render_template(
            template=sample_template,
            variables={"user_name": "John Doe"},
            language="fr"  # Non-existent language
        )
        
        # Should fallback to Vietnamese
        assert rendered["title"] == "Test John Doe"
        assert rendered["body"] == "Hello John Doe!"


class TestDeviceTokenManagement:
    """Test device token CRUD operations"""
    
    @pytest.fixture
    def sample_user(self, test_db_session):
        """Create sample user for testing"""
        user = User(
            email="<EMAIL>",
            display_name="Device Test User",
            auth_provider="google",
            provider_user_id="google_device_123"
        )
        test_db_session.add(user)
        test_db_session.commit()
        test_db_session.refresh(user)
        return user
    
    def test_register_new_device_token(self, test_db_session, sample_user):
        """Test registering a new device token"""
        from app.crud.crud_device_token import device_token
        
        token = device_token.register_or_update(
            db=test_db_session,
            user_id=sample_user.user_id,
            device_token="new_fcm_token",
            device_type="android",
            device_id="android_device_123"
        )
        
        assert token.user_id == sample_user.user_id
        assert token.device_token == "new_fcm_token"
        assert token.device_type == "android"
        assert token.is_active is True
    
    def test_update_existing_device_token(self, test_db_session, sample_user):
        """Test updating an existing device token"""
        from app.crud.crud_device_token import device_token
        
        # Create initial token
        initial_token = device_token.register_or_update(
            db=test_db_session,
            user_id=sample_user.user_id,
            device_token="initial_token",
            device_type="ios",
            device_id="ios_device_123"
        )
        
        # Update with new token for same device
        updated_token = device_token.register_or_update(
            db=test_db_session,
            user_id=sample_user.user_id,
            device_token="updated_token",
            device_type="ios",
            device_id="ios_device_123"  # Same device ID
        )
        
        # Should update the existing record
        assert updated_token.token_id == initial_token.token_id
        assert updated_token.device_token == "updated_token"
    
    def test_deactivate_device_token(self, test_db_session, sample_user):
        """Test deactivating a device token"""
        from app.crud.crud_device_token import device_token
        
        token = device_token.register_or_update(
            db=test_db_session,
            user_id=sample_user.user_id,
            device_token="token_to_deactivate",
            device_type="web"
        )
        
        success = device_token.deactivate_token(
            db=test_db_session,
            token_id=token.token_id
        )
        
        assert success is True
        
        # Refresh and check
        test_db_session.refresh(token)
        assert token.is_active is False


class TestNotificationTemplates:
    """Test notification template management"""
    
    def test_create_template(self, test_db_session):
        """Test creating a notification template"""
        from app.crud.crud_notification_template import notification_template
        
        template_data = NotificationTemplateCreate(
            template_key="test_create_template",
            template_name="Test Create Template",
            category="marketing",
            notification_type="test_create",
            title_templates={"vi": "Test Title", "en": "Test Title"},
            body_templates={"vi": "Test Body", "en": "Test Body"},
            priority="normal"
        )
        
        template = notification_template.create_template(
            db=test_db_session,
            obj_in=template_data
        )
        
        assert template.template_key == "test_create_template"
        assert template.is_active is True
    
    def test_create_duplicate_template_key(self, test_db_session):
        """Test creating template with duplicate key should fail"""
        from app.crud.crud_notification_template import notification_template
        
        template_data = NotificationTemplateCreate(
            template_key="duplicate_key",
            template_name="First Template",
            category="marketing",
            notification_type="test",
            title_templates={"vi": "Title", "en": "Title"},
            body_templates={"vi": "Body", "en": "Body"}
        )
        
        # Create first template
        notification_template.create_template(db=test_db_session, obj_in=template_data)
        
        # Try to create duplicate
        with pytest.raises(ValueError, match="already exists"):
            notification_template.create_template(db=test_db_session, obj_in=template_data)
    
    def test_template_validation(self, test_db_session):
        """Test template validation rules"""
        from app.crud.crud_notification_template import notification_template
        
        # Missing required language
        template_data = NotificationTemplateCreate(
            template_key="invalid_template",
            template_name="Invalid Template",
            category="marketing",
            notification_type="test",
            title_templates={"vi": "Title"},  # Missing English
            body_templates={"vi": "Body", "en": "Body"}
        )
        
        with pytest.raises(ValueError, match="Missing title template for language: en"):
            notification_template.create_template(db=test_db_session, obj_in=template_data)
