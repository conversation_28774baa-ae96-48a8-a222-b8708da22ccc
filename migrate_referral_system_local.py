#!/usr/bin/env python3
"""
Local Migration script for Referral System
This script runs on local machine connecting to localhost:5432
"""

import sys
import os
from sqlalchemy import text, create_engine
from sqlalchemy.orm import sessionmaker

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(__file__))

# Try different database URLs
DATABASE_URLS = [
    "postgresql://banachef_admin:123453Ago@localhost:5432/banachef_db",  # Docker database
    "postgresql://postgres@localhost:5432/banachef_db",  # Local PostgreSQL with default user
    "postgresql://postgres:postgres@localhost:5432/banachef_db",  # Local PostgreSQL with password
]

def get_working_database_url():
    """Try different database URLs to find one that works"""
    for url in DATABASE_URLS:
        try:
            test_engine = create_engine(url, pool_pre_ping=True)
            with test_engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            print(f"✅ Connected using: {url}")
            return url, test_engine
        except Exception as e:
            print(f"❌ Failed to connect with {url}: {e}")
            continue

    print("❌ Could not connect to any database")
    print("💡 Please ensure one of the following:")
    print("   1. Docker database is running: docker-compose up -d db")
    print("   2. Local PostgreSQL is running with 'banachef_db' database")
    print("   3. Create the database: createdb banachef_db")
    return None, None

# Get working database connection
LOCAL_DATABASE_URL, local_engine = get_working_database_url()
if not LOCAL_DATABASE_URL:
    sys.exit(1)

# Engine will be created by get_working_database_url() function

# Create SessionLocal class
LocalSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=local_engine)

from app.models.user import User
from app.services.referral_service import referral_service


def create_referral_tables():
    """Create new tables for referral system"""
    print("📋 Creating referral system tables...")
    
    # SQL to create referral_history table
    create_referral_history_sql = """
    CREATE TABLE IF NOT EXISTS referral_history (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        referrer_user_id UUID NOT NULL REFERENCES users(user_id),
        referred_user_id UUID NOT NULL REFERENCES users(user_id),
        status VARCHAR(20) NOT NULL DEFAULT 'pending',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );
    
    CREATE INDEX IF NOT EXISTS idx_referral_history_referrer ON referral_history(referrer_user_id);
    CREATE INDEX IF NOT EXISTS idx_referral_history_referred ON referral_history(referred_user_id);
    """
    
    # SQL to create referral_vouchers table
    create_referral_vouchers_sql = """
    CREATE TABLE IF NOT EXISTS referral_vouchers (
        voucher_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        owner_user_id UUID NOT NULL REFERENCES users(user_id),
        source_referral_id UUID NOT NULL REFERENCES referral_history(id),
        amount_usd NUMERIC(5, 2) NOT NULL DEFAULT 5.00,
        status VARCHAR(20) NOT NULL DEFAULT 'available',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        used_at TIMESTAMP WITH TIME ZONE,
        expires_at TIMESTAMP WITH TIME ZONE
    );
    
    CREATE INDEX IF NOT EXISTS idx_referral_vouchers_owner ON referral_vouchers(owner_user_id);
    CREATE INDEX IF NOT EXISTS idx_referral_vouchers_status ON referral_vouchers(status);
    """
    
    try:
        with local_engine.connect() as conn:
            # Create referral_history table first (referenced by referral_vouchers)
            conn.execute(text(create_referral_history_sql))
            print("✅ Created referral_history table")
            
            # Create referral_vouchers table
            conn.execute(text(create_referral_vouchers_sql))
            print("✅ Created referral_vouchers table")
            
            conn.commit()
        return True
    except Exception as e:
        print(f"❌ Error creating referral tables: {e}")
        return False


def add_referral_columns_to_users():
    """Add referral columns to users table"""
    print("📋 Adding referral columns to users table...")
    
    add_columns_sql = """
    -- Add referral_code column
    DO $$ 
    BEGIN 
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                      WHERE table_name='users' AND column_name='referral_code') THEN
            ALTER TABLE users ADD COLUMN referral_code VARCHAR(50) UNIQUE;
            CREATE INDEX IF NOT EXISTS idx_users_referral_code ON users(referral_code);
        END IF;
    END $$;
    
    -- Add referred_by_user_id column
    DO $$ 
    BEGIN 
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                      WHERE table_name='users' AND column_name='referred_by_user_id') THEN
            ALTER TABLE users ADD COLUMN referred_by_user_id UUID REFERENCES users(user_id);
        END IF;
    END $$;
    
    -- Add has_made_first_purchase column
    DO $$ 
    BEGIN 
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                      WHERE table_name='users' AND column_name='has_made_first_purchase') THEN
            ALTER TABLE users ADD COLUMN has_made_first_purchase BOOLEAN NOT NULL DEFAULT FALSE;
        END IF;
    END $$;
    """
    
    try:
        with local_engine.connect() as conn:
            conn.execute(text(add_columns_sql))
            conn.commit()
        print("✅ Added referral columns to users table")
        return True
    except Exception as e:
        print(f"❌ Error adding referral columns: {e}")
        return False


def generate_referral_codes_for_existing_users():
    """Generate referral codes for existing users who don't have one"""
    print("📋 Generating referral codes for existing users...")
    
    db = LocalSessionLocal()
    try:
        # Get all users without referral codes
        users_without_codes = db.query(User).filter(User.referral_code.is_(None)).all()
        
        print(f"Found {len(users_without_codes)} users without referral codes")
        
        for user in users_without_codes:
            try:
                # Generate referral code
                referral_code = referral_service.generate_referral_code(
                    db, 
                    user.display_name or user.email.split('@')[0]
                )
                
                # Update user
                user.referral_code = referral_code
                db.add(user)
                
                print(f"✅ Generated code {referral_code} for user {user.email}")
                
            except Exception as e:
                print(f"❌ Error generating code for user {user.email}: {e}")
                continue
        
        db.commit()
        print(f"✅ Successfully generated referral codes for {len(users_without_codes)} users")
        return True
        
    except Exception as e:
        print(f"❌ Error generating referral codes: {e}")
        db.rollback()
        return False
    finally:
        db.close()


def verify_migration():
    """Verify that migration was successful"""
    print("📋 Verifying migration...")
    
    verification_sql = """
    -- Check if tables exist
    SELECT 
        CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'referral_history') 
             THEN 'referral_history: ✅' 
             ELSE 'referral_history: ❌' END as referral_history_status,
        CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'referral_vouchers') 
             THEN 'referral_vouchers: ✅' 
             ELSE 'referral_vouchers: ❌' END as referral_vouchers_status,
        CASE WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='users' AND column_name='referral_code') 
             THEN 'users.referral_code: ✅' 
             ELSE 'users.referral_code: ❌' END as referral_code_status,
        CASE WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='users' AND column_name='referred_by_user_id') 
             THEN 'users.referred_by_user_id: ✅' 
             ELSE 'users.referred_by_user_id: ❌' END as referred_by_status,
        CASE WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='users' AND column_name='has_made_first_purchase') 
             THEN 'users.has_made_first_purchase: ✅' 
             ELSE 'users.has_made_first_purchase: ❌' END as first_purchase_status;
    """
    
    try:
        with local_engine.connect() as conn:
            result = conn.execute(text(verification_sql)).fetchone()
            
            print("\n📊 Migration Verification Results:")
            for key, value in result._mapping.items():
                print(f"   {value}")
            
        # Check referral codes
        db = LocalSessionLocal()
        try:
            total_users = db.query(User).count()
            users_with_codes = db.query(User).filter(User.referral_code.isnot(None)).count()
            print(f"   Referral codes: {users_with_codes}/{total_users} users have codes")
        finally:
            db.close()
            
        return True
    except Exception as e:
        print(f"❌ Error verifying migration: {e}")
        return False


def main():
    """Main migration function"""
    print("🚀 Starting Referral System Migration (Local)...")

    # Database connection is already tested in get_working_database_url()
    if not LOCAL_DATABASE_URL or not local_engine:
        sys.exit(1)

    print(f"📊 Using Database URL: {LOCAL_DATABASE_URL}")
    
    # Step 1: Add columns to users table
    if not add_referral_columns_to_users():
        print("❌ Failed to add referral columns to users table")
        sys.exit(1)
    
    # Step 2: Create referral tables
    if not create_referral_tables():
        print("❌ Failed to create referral tables")
        sys.exit(1)
    
    # Step 3: Generate referral codes for existing users
    if not generate_referral_codes_for_existing_users():
        print("❌ Failed to generate referral codes")
        sys.exit(1)
    
    # Step 4: Verify migration
    if not verify_migration():
        print("❌ Migration verification failed")
        sys.exit(1)
    
    print("\n🎉 Referral System Migration completed successfully!")
    print("\n📋 What was created:")
    print("   - referral_history table")
    print("   - referral_vouchers table") 
    print("   - users.referral_code column")
    print("   - users.referred_by_user_id column")
    print("   - users.has_made_first_purchase column")
    print("   - Referral codes for all existing users")


if __name__ == "__main__":
    main()
