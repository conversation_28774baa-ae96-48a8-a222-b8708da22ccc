# 🚀 Push Notification System - Deployment Guide

Complete deployment guide for the Bana Chef push notification system in production environments.

## 📋 Prerequisites

### 1. Firebase Setup

1. **Create Firebase Project**
   ```bash
   # Go to Firebase Console
   https://console.firebase.google.com/
   
   # Create new project: "banachef-production"
   # Enable Firebase Cloud Messaging (FCM)
   ```

2. **Generate Service Account**
   ```bash
   # In Firebase Console:
   # Project Settings > Service Accounts > Generate New Private Key
   # Download JSON file as: firebase-service-account.json
   ```

3. **Configure Client Apps**
   ```bash
   # Add iOS app with bundle ID: com.banachef.app
   # Add Android app with package name: com.banachef.app
   # Add Web app with domain: banachef.com
   ```

### 2. Infrastructure Requirements

- **Server**: 2+ CPU cores, 4GB+ RAM
- **Database**: PostgreSQL 15+
- **Cache**: Redis 7+
- **Queue**: Redis (same instance or separate)
- **Monitoring**: Prometheus + <PERSON>ana (optional)

## 🔧 Production Configuration

### 1. Environment Variables

Create production `.env` file:

```bash
# Application
APP_NAME=Bana Chef Server
DEBUG=false
LOG_LEVEL=info
SECRET_KEY=your-super-secure-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here

# Database
DATABASE_URL=**************************************************/banachef_prod
DB_USER=banachef_user
DB_PASSWORD=secure_password

# Firebase
FIREBASE_PROJECT_ID=banachef-production
FIREBASE_CREDENTIALS_PATH=/app/credentials/firebase-service-account.json

# Redis & Celery
REDIS_URL=redis://redis:6379/0
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0

# Notification Settings
NOTIFICATION_BATCH_SIZE=500
NOTIFICATION_RETRY_ATTEMPTS=3
NOTIFICATION_DEFAULT_LANGUAGE=vi
NOTIFICATION_RATE_LIMIT_PER_USER=100

# Scheduled Tasks
ENABLE_SCHEDULED_NOTIFICATIONS=true
DAILY_RECIPE_SUGGESTION_TIME=02:00  # 9AM Vietnam time (UTC+7)
WEEKLY_SUMMARY_DAY=0

# CORS
CORS_ORIGINS=https://banachef.com,https://www.banachef.com,https://admin.banachef.com

# Monitoring
GRAFANA_PASSWORD=secure_grafana_password
```

### 2. Docker Production Setup

**docker-compose.prod.yml**:
```yaml
version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    container_name: banachef_db_prod
    restart: unless-stopped
    environment:
      POSTGRES_DB: banachef_prod
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
      - ./backups:/backups
    ports:
      - "127.0.0.1:5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER} -d banachef_prod"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis
  redis:
    image: redis:7-alpine
    container_name: banachef_redis_prod
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data_prod:/data
    ports:
      - "127.0.0.1:6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # FastAPI Application
  api:
    image: banachef-server:latest
    container_name: banachef_api_prod
    restart: unless-stopped
    environment:
      - DATABASE_URL=postgresql://${DB_USER}:${DB_PASSWORD}@db:5432/banachef_prod
      - REDIS_URL=redis://redis:6379/0
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - FIREBASE_CREDENTIALS_PATH=/app/credentials/firebase-service-account.json
      - DEBUG=false
      - LOG_LEVEL=info
    ports:
      - "127.0.0.1:8000:8000"
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./credentials:/app/credentials:ro
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Worker
  celery_worker:
    image: banachef-server:latest
    container_name: banachef_celery_worker_prod
    restart: unless-stopped
    environment:
      - DATABASE_URL=postgresql://${DB_USER}:${DB_PASSWORD}@db:5432/banachef_prod
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - FIREBASE_CREDENTIALS_PATH=/app/credentials/firebase-service-account.json
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./credentials:/app/credentials:ro
      - ./logs:/app/logs
    command: celery -A app.core.celery_app worker --loglevel=info --concurrency=4 --queues=notifications,scheduled

  # Celery Beat
  celery_beat:
    image: banachef-server:latest
    container_name: banachef_celery_beat_prod
    restart: unless-stopped
    environment:
      - DATABASE_URL=postgresql://${DB_USER}:${DB_PASSWORD}@db:5432/banachef_prod
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./credentials:/app/credentials:ro
      - ./logs:/app/logs
      - celery_beat_data_prod:/app/celerybeat-schedule
    command: celery -A app.core.celery_app beat --loglevel=info

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: banachef_nginx_prod
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - api

volumes:
  postgres_data_prod:
  redis_data_prod:
  celery_beat_data_prod:
```

### 3. Nginx Configuration

**nginx/nginx.prod.conf**:
```nginx
events {
    worker_connections 1024;
}

http {
    upstream banachef_api {
        server api:8000;
    }

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=notifications:10m rate=5r/s;

    server {
        listen 80;
        server_name api.banachef.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name api.banachef.com;

        ssl_certificate /etc/nginx/ssl/banachef.crt;
        ssl_certificate_key /etc/nginx/ssl/banachef.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers HIGH:!aNULL:!MD5;

        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

        # API endpoints
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://banachef_api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Notification endpoints with stricter rate limiting
        location /api/v1/notifications/ {
            limit_req zone=notifications burst=10 nodelay;
            proxy_pass http://banachef_api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Health check
        location /health {
            proxy_pass http://banachef_api;
            access_log off;
        }
    }
}
```

## 🚀 Deployment Steps

### 1. Server Preparation

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Create application directory
sudo mkdir -p /opt/banachef
sudo chown $USER:$USER /opt/banachef
cd /opt/banachef
```

### 2. Deploy Application

```bash
# Clone repository
git clone https://github.com/your-org/banachef-server.git .

# Create production environment
cp .env.example .env.prod
# Edit .env.prod with production values

# Create credentials directory
mkdir -p credentials
# Copy Firebase service account JSON file to credentials/

# Create logs directory
mkdir -p logs/nginx

# Build production image
docker build -t banachef-server:latest .

# Start services
docker-compose -f docker-compose.prod.yml up -d

# Initialize database
docker-compose -f docker-compose.prod.yml exec api python create_tables.py
docker-compose -f docker-compose.prod.yml exec api python seed_notification_templates.py
```

### 3. SSL Certificate Setup

```bash
# Using Let's Encrypt
sudo apt install certbot
sudo certbot certonly --standalone -d api.banachef.com

# Copy certificates
sudo mkdir -p /opt/banachef/nginx/ssl
sudo cp /etc/letsencrypt/live/api.banachef.com/fullchain.pem /opt/banachef/nginx/ssl/banachef.crt
sudo cp /etc/letsencrypt/live/api.banachef.com/privkey.pem /opt/banachef/nginx/ssl/banachef.key
sudo chown -R $USER:$USER /opt/banachef/nginx/ssl

# Restart nginx
docker-compose -f docker-compose.prod.yml restart nginx
```

## 📊 Monitoring Setup

### 1. Health Checks

```bash
# API health
curl https://api.banachef.com/health

# Database health
docker-compose -f docker-compose.prod.yml exec db pg_isready -U banachef_user

# Redis health
docker-compose -f docker-compose.prod.yml exec redis redis-cli ping

# Celery workers
docker-compose -f docker-compose.prod.yml exec celery_worker celery -A app.core.celery_app inspect active
```

### 2. Log Management

```bash
# View application logs
docker-compose -f docker-compose.prod.yml logs -f api

# View Celery logs
docker-compose -f docker-compose.prod.yml logs -f celery_worker
docker-compose -f docker-compose.prod.yml logs -f celery_beat

# View Nginx logs
tail -f logs/nginx/access.log
tail -f logs/nginx/error.log
```

### 3. Performance Monitoring

```bash
# Start monitoring stack
docker-compose -f docker-compose.prod.yml --profile monitoring up -d

# Access Grafana
open https://monitoring.banachef.com:3000
# Default: admin / (password from .env)
```

## 🔒 Security Checklist

- [ ] **Firewall configured** (only ports 80, 443, 22 open)
- [ ] **SSL certificates** installed and auto-renewal setup
- [ ] **Database credentials** are strong and unique
- [ ] **Firebase service account** has minimal permissions
- [ ] **Rate limiting** configured in Nginx
- [ ] **Security headers** added to Nginx
- [ ] **Log rotation** configured
- [ ] **Backup strategy** implemented
- [ ] **Monitoring alerts** configured

## 📈 Scaling Considerations

### Horizontal Scaling

```bash
# Scale Celery workers
docker-compose -f docker-compose.prod.yml up -d --scale celery_worker=3

# Load balancer configuration for multiple API instances
# Use external load balancer (AWS ALB, GCP Load Balancer, etc.)
```

### Database Optimization

```sql
-- Create additional indexes for performance
CREATE INDEX CONCURRENTLY idx_notification_logs_created_status ON notification_logs(created_at, delivery_status);
CREATE INDEX CONCURRENTLY idx_device_tokens_user_type_active ON device_tokens(user_id, device_type, is_active);

-- Partition large tables (if needed)
-- Consider partitioning notification_logs by date
```

### Redis Optimization

```bash
# Redis configuration for production
# In docker-compose.prod.yml, update Redis command:
command: redis-server --appendonly yes --maxmemory 2gb --maxmemory-policy allkeys-lru --save 900 1 --save 300 10
```

## 🔄 Maintenance

### Daily Tasks

```bash
# Check service health
./scripts/health_check.sh

# Monitor disk usage
df -h

# Check logs for errors
grep -i error logs/nginx/error.log | tail -20
```

### Weekly Tasks

```bash
# Database backup
./scripts/backup_database.sh

# Clean old logs
find logs/ -name "*.log" -mtime +7 -delete

# Update SSL certificates (if needed)
sudo certbot renew
```

### Monthly Tasks

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Review and rotate logs
logrotate -f /etc/logrotate.conf

# Performance review
# Check Grafana dashboards for trends
```

## 🆘 Troubleshooting

### Common Issues

1. **High Memory Usage**
   ```bash
   # Check memory usage
   docker stats
   
   # Restart services if needed
   docker-compose -f docker-compose.prod.yml restart celery_worker
   ```

2. **Failed Notifications**
   ```bash
   # Check Firebase credentials
   docker-compose -f docker-compose.prod.yml exec api python -c "import firebase_admin; print('Firebase OK')"
   
   # Check notification logs
   docker-compose -f docker-compose.prod.yml exec api python -c "
   from app.db.session import SessionLocal
   from app.models.notification_log import NotificationLog
   db = SessionLocal()
   failed = db.query(NotificationLog).filter(NotificationLog.delivery_status == 'failed').count()
   print(f'Failed notifications: {failed}')
   "
   ```

3. **Database Connection Issues**
   ```bash
   # Check database connectivity
   docker-compose -f docker-compose.prod.yml exec api python -c "
   from app.db.session import SessionLocal
   db = SessionLocal()
   print('Database connection OK')
   "
   ```

---

**🎉 Your push notification system is now deployed and ready for production!**

For support, monitoring, and maintenance, refer to the main documentation and monitoring dashboards.
