#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to run all tests for Bana Chef Server.
"""

import sys
import os
import subprocess


def run_import_tests():
    """Run import tests."""
    print("🧪 Running import tests...")
    try:
        result = subprocess.run([
            sys.executable, "tests/test_imports.py"
        ], capture_output=True, text=True, cwd=os.getcwd())
        
        if result.returncode == 0:
            print("✅ Import tests passed")
            print(result.stdout)
            return True
        else:
            print("❌ Import tests failed")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Error running import tests: {e}")
        return False


def run_integration_tests():
    """Run database integration tests."""
    print("🧪 Running database integration tests...")
    try:
        result = subprocess.run([
            sys.executable, "tests/test_database_integration.py"
        ], capture_output=True, text=True, cwd=os.getcwd())
        
        if result.returncode == 0:
            print("✅ Integration tests passed")
            print(result.stdout)
            return True
        else:
            print("❌ Integration tests failed")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Error running integration tests: {e}")
        return False


def run_pytest():
    """Run pytest tests."""
    print("🧪 Running pytest tests...")
    try:
        # Check if pytest is available
        result = subprocess.run([
            sys.executable, "-m", "pytest", "--version"
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            print("⚠️  pytest not installed, skipping pytest tests")
            print("   Install with: pip install pytest pytest-asyncio")
            return True  # Don't fail if pytest is not installed
        
        # Run pytest
        result = subprocess.run([
            sys.executable, "-m", "pytest", "tests/", "-v"
        ], capture_output=True, text=True, cwd=os.getcwd())
        
        print(result.stdout)
        if result.stderr:
            print(result.stderr)
        
        if result.returncode == 0:
            print("✅ Pytest tests passed")
            return True
        else:
            print("❌ Pytest tests failed")
            return False
            
    except Exception as e:
        print(f"❌ Error running pytest: {e}")
        return False


def main():
    """Run all tests."""
    print("🚀 Starting Bana Chef Server tests...")
    print("=" * 50)
    
    tests = [
        ("Import Tests", run_import_tests),
        ("Integration Tests", run_integration_tests),
        ("Pytest Tests", run_pytest)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} completed successfully")
            else:
                print(f"❌ {test_name} failed")
        except Exception as e:
            print(f"❌ {test_name} error: {e}")
        
        print()
    
    print("=" * 50)
    print(f"📊 Test Summary: {passed}/{total} test suites passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        return True
    else:
        print("❌ Some tests failed!")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
