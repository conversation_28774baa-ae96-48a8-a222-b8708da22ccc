#!/bin/bash

# Test Docker build and run script for BanaChef Server

set -e

echo "🐳 Testing BanaChef Server Docker Build & Run"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Test 1: Build Docker image
echo -e "\n${YELLOW}📦 Step 1: Building Docker image...${NC}"
if docker build -t banachef-server:test .; then
    print_status "Docker image built successfully"
else
    print_error "Failed to build Docker image"
    exit 1
fi

# Test 2: Check if image exists
echo -e "\n${YELLOW}🔍 Step 2: Checking Docker image...${NC}"
if docker images | grep -q "banachef-server.*test"; then
    print_status "Docker image found"
    docker images | grep "banachef-server.*test"
else
    print_error "Docker image not found"
    exit 1
fi

# Test 3: Test container startup (without dependencies)
echo -e "\n${YELLOW}🚀 Step 3: Testing container startup...${NC}"
CONTAINER_ID=$(docker run -d \
    -p 8001:8000 \
    -e DATABASE_URL="sqlite:///tmp/test.db" \
    -e SECRET_KEY="test-secret-key" \
    -e JWT_SECRET_KEY="test-jwt-secret" \
    -e DEBUG="True" \
    --name banachef-test \
    banachef-server:test)

if [ $? -eq 0 ]; then
    print_status "Container started with ID: $CONTAINER_ID"
else
    print_error "Failed to start container"
    exit 1
fi

# Wait for container to start
echo -e "\n${YELLOW}⏳ Waiting for container to start...${NC}"
sleep 10

# Test 4: Check container health
echo -e "\n${YELLOW}🏥 Step 4: Checking container health...${NC}"
if docker ps | grep -q "banachef-test"; then
    print_status "Container is running"
    docker ps | grep "banachef-test"
else
    print_warning "Container may not be running properly"
    echo "Container logs:"
    docker logs banachef-test
fi

# Test 5: Test API endpoints
echo -e "\n${YELLOW}🌐 Step 5: Testing API endpoints...${NC}"

# Test root endpoint
if curl -f -s http://localhost:8001/ > /dev/null; then
    print_status "Root endpoint (/) is accessible"
else
    print_warning "Root endpoint (/) is not accessible"
fi

# Test docs endpoint
if curl -f -s http://localhost:8001/docs > /dev/null; then
    print_status "Docs endpoint (/docs) is accessible"
else
    print_warning "Docs endpoint (/docs) is not accessible"
fi

# Test health endpoint (may fail due to DB connection)
if curl -f -s http://localhost:8001/health > /dev/null; then
    print_status "Health endpoint (/health) is accessible"
else
    print_warning "Health endpoint (/health) is not accessible (expected if no DB)"
fi

# Test 6: Check container logs
echo -e "\n${YELLOW}📋 Step 6: Container logs (last 20 lines):${NC}"
docker logs --tail 20 banachef-test

# Cleanup
echo -e "\n${YELLOW}🧹 Cleanup: Stopping and removing test container...${NC}"
docker stop banachef-test > /dev/null 2>&1 || true
docker rm banachef-test > /dev/null 2>&1 || true

print_status "Test completed!"

echo -e "\n${GREEN}🎉 Docker build and basic functionality test completed!${NC}"
echo -e "${YELLOW}📝 Next steps:${NC}"
echo "   1. Start full stack with: docker-compose up"
echo "   2. Test with database connectivity"
echo "   3. Test notification features with Firebase setup"
