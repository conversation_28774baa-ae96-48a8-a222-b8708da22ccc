name: Dependency Updates

on:
  schedule:
    # Run every Monday at 9 AM UTC
    - cron: '0 9 * * 1'
  workflow_dispatch:

jobs:
  # Job 1: Check for dependency updates
  check-dependencies:
    runs-on: ubuntu-latest
    outputs:
      has-updates: ${{ steps.check.outputs.has-updates }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.11"

    - name: Install pip-tools
      run: |
        python -m pip install --upgrade pip
        pip install pip-tools

    - name: Check for outdated dependencies
      id: check
      run: |
        pip-compile --upgrade --dry-run requirements.in > updated_requirements.txt 2>&1 || true
        if ! diff -q requirements.txt updated_requirements.txt > /dev/null 2>&1; then
          echo "has-updates=true" >> $GITHUB_OUTPUT
          echo "Dependencies have updates available"
        else
          echo "has-updates=false" >> $GITHUB_OUTPUT
          echo "All dependencies are up to date"
        fi

    - name: Upload updated requirements
      if: steps.check.outputs.has-updates == 'true'
      uses: actions/upload-artifact@v3
      with:
        name: updated-requirements
        path: updated_requirements.txt

  # Job 2: Create PR for dependency updates
  create-update-pr:
    runs-on: ubuntu-latest
    needs: check-dependencies
    if: needs.check-dependencies.outputs.has-updates == 'true'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.11"

    - name: Install pip-tools
      run: |
        python -m pip install --upgrade pip
        pip install pip-tools

    - name: Update dependencies
      run: |
        pip-compile --upgrade requirements.in
        pip-compile --upgrade requirements-dev.in || true

    - name: Check for security vulnerabilities
      run: |
        pip install safety
        safety check --requirement requirements.txt --json --output safety-report.json || true

    - name: Create Pull Request
      uses: peter-evans/create-pull-request@v5
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        commit-message: "chore: update dependencies"
        title: "🔄 Automated Dependency Updates"
        body: |
          ## 🔄 Automated Dependency Updates
          
          This PR contains automated dependency updates generated by the dependency update workflow.
          
          ### Changes
          - Updated Python dependencies to their latest compatible versions
          - Security vulnerabilities check completed
          
          ### Testing
          - [ ] All CI tests pass
          - [ ] Manual testing completed (if required)
          - [ ] Security scan results reviewed
          
          ### Notes
          Please review the changes carefully before merging, especially for major version updates.
          
          **Auto-generated by GitHub Actions**
        branch: automated/dependency-updates
        delete-branch: true
        labels: |
          dependencies
          automated
          chore

  # Job 3: Security audit
  security-audit:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.11"

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install safety bandit pip-audit

    - name: Run safety check
      run: |
        safety check --requirement requirements.txt --json --output safety-report.json || true

    - name: Run pip-audit
      run: |
        pip-audit --requirement requirements.txt --format=json --output=pip-audit-report.json || true

    - name: Run bandit security scan
      run: |
        bandit -r app/ -f json -o bandit-report.json || true

    - name: Upload security reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-audit-reports
        path: |
          safety-report.json
          pip-audit-report.json
          bandit-report.json

    - name: Create security issue if vulnerabilities found
      if: failure()
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          
          let vulnerabilities = [];
          
          // Check safety report
          try {
            const safetyReport = JSON.parse(fs.readFileSync('safety-report.json', 'utf8'));
            if (safetyReport.vulnerabilities && safetyReport.vulnerabilities.length > 0) {
              vulnerabilities.push(...safetyReport.vulnerabilities);
            }
          } catch (e) {
            console.log('No safety report found or error reading it');
          }
          
          if (vulnerabilities.length > 0) {
            const issueBody = `
          ## 🚨 Security Vulnerabilities Detected
          
          The automated security scan has detected ${vulnerabilities.length} vulnerability(ies) in our dependencies.
          
          ### Vulnerabilities Found:
          ${vulnerabilities.map(v => `- **${v.package_name}** (${v.installed_version}): ${v.advisory}`).join('\n')}
          
          ### Action Required:
          1. Review the vulnerabilities listed above
          2. Update the affected packages to secure versions
          3. Test the application after updates
          4. Close this issue once resolved
          
          **Auto-generated by Security Audit Workflow**
            `;
            
            github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: '🚨 Security Vulnerabilities Detected in Dependencies',
              body: issueBody,
              labels: ['security', 'vulnerability', 'high-priority']
            });
          }
