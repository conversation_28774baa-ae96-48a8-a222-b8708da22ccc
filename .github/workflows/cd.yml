name: CD Pipeline

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
        - staging
        - production

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Job 1: Build and Push Docker Image
  build-and-push:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    outputs:
      image-digest: ${{ steps.build.outputs.digest }}
      image-tag: ${{ steps.meta.outputs.tags }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: .
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64,linux/arm64

  # Job 2: Deploy to Staging
  deploy-staging:
    runs-on: ubuntu-latest
    needs: build-and-push
    if: github.ref == 'refs/heads/main' || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'staging')
    environment:
      name: staging
      url: https://staging-api.banachef.com
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        echo "Image: ${{ needs.build-and-push.outputs.image-tag }}"
        # Add your staging deployment logic here
        # This could be:
        # - SSH to staging server and update docker-compose
        # - Deploy to Kubernetes
        # - Deploy to cloud platform (AWS ECS, Google Cloud Run, etc.)

    - name: Run smoke tests
      run: |
        echo "Running smoke tests against staging..."
        # Add smoke tests here
        # curl -f https://staging-api.banachef.com/health

    - name: Notify deployment status
      if: always()
      run: |
        echo "Staging deployment completed"
        # Add notification logic (Slack, Discord, email, etc.)

  # Job 3: Deploy to Production
  deploy-production:
    runs-on: ubuntu-latest
    needs: [build-and-push, deploy-staging]
    if: startsWith(github.ref, 'refs/tags/v') || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production')
    environment:
      name: production
      url: https://api.banachef.com
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Deploy to production
      run: |
        echo "Deploying to production environment..."
        echo "Image: ${{ needs.build-and-push.outputs.image-tag }}"
        # Add your production deployment logic here

    - name: Run production health checks
      run: |
        echo "Running production health checks..."
        # Add comprehensive health checks here

    - name: Notify production deployment
      if: always()
      run: |
        echo "Production deployment completed"
        # Add notification logic

  # Job 4: Database Migration (if needed)
  database-migration:
    runs-on: ubuntu-latest
    needs: build-and-push
    if: github.ref == 'refs/heads/main' || startsWith(github.ref, 'refs/tags/v')
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.11"

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Run database migrations (staging)
      if: github.ref == 'refs/heads/main'
      env:
        DATABASE_URL: ${{ secrets.STAGING_DATABASE_URL }}
      run: |
        echo "Running database migrations for staging..."
        # python migrate.py or alembic upgrade head

    - name: Run database migrations (production)
      if: startsWith(github.ref, 'refs/tags/v')
      env:
        DATABASE_URL: ${{ secrets.PRODUCTION_DATABASE_URL }}
      run: |
        echo "Running database migrations for production..."
        # python migrate.py or alembic upgrade head

  # Job 5: Security Scan of Built Image
  security-scan:
    runs-on: ubuntu-latest
    needs: build-and-push
    permissions:
      security-events: write
    
    steps:
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ needs.build-and-push.outputs.image-tag }}
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  # Job 6: Performance Testing
  performance-test:
    runs-on: ubuntu-latest
    needs: deploy-staging
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run performance tests
      run: |
        echo "Running performance tests against staging..."
        # Add performance testing tools like:
        # - Artillery.io
        # - k6
        # - Apache Bench
        # - Custom load testing scripts

    - name: Upload performance results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: performance-results
        path: performance-results/
