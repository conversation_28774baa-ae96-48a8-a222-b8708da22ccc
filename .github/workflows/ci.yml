name: CI Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  PYTHON_VERSION: "3.11"
  POETRY_VERSION: "1.6.1"

jobs:
  # Job 1: Code Quality & Security
  code-quality:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install bandit safety black isort flake8

    - name: Code formatting check (Black)
      run: black --check --diff app/ tests/

    - name: Import sorting check (isort)
      run: isort --check-only --diff app/ tests/

    - name: Linting (flake8)
      run: flake8 app/ tests/ --max-line-length=88 --extend-ignore=E203,W503

    - name: Security scan (Bandit)
      run: bandit -r app/ -f json -o bandit-report.json || true

    - name: Dependency vulnerability scan (Safety)
      run: safety check --json --output safety-report.json || true

    - name: Upload security reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-reports
        path: |
          bandit-report.json
          safety-report.json

  # Job 2: Unit Tests
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.10", "3.11", "3.12"]
    
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ matrix.python-version }}-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-${{ matrix.python-version }}-

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest-cov pytest-xdist

    - name: Set up test environment
      run: |
        cp .env.example .env
        echo "DATABASE_URL=postgresql://test_user:test_password@localhost:5432/test_db" >> .env
        echo "SECRET_KEY=test-secret-key" >> .env
        echo "JWT_SECRET_KEY=test-jwt-secret" >> .env

    - name: Run tests with coverage
      run: |
        pytest tests/ -v --cov=app --cov-report=xml --cov-report=html --cov-report=term-missing

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false

    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results-${{ matrix.python-version }}
        path: |
          coverage.xml
          htmlcov/

  # Job 3: Integration Tests
  integration-test:
    runs-on: ubuntu-latest
    needs: [code-quality, test]
    
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_PASSWORD: integration_test_password
          POSTGRES_USER: integration_test_user
          POSTGRES_DB: integration_test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Set up integration test environment
      run: |
        cp .env.example .env
        echo "DATABASE_URL=postgresql://integration_test_user:integration_test_password@localhost:5432/integration_test_db" >> .env
        echo "SECRET_KEY=integration-test-secret-key" >> .env
        echo "JWT_SECRET_KEY=integration-test-jwt-secret" >> .env

    - name: Run database migrations
      run: |
        python create_tables.py

    - name: Run integration tests
      run: |
        pytest tests/test_database_integration.py -v --tb=short

  # Job 4: Docker Build Test
  docker-build:
    runs-on: ubuntu-latest
    needs: [code-quality, test]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Build Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        push: false
        tags: banachef-server:test
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Test Docker image
      run: |
        docker run --rm -d --name test-container -p 8000:8000 banachef-server:test
        sleep 10
        curl -f http://localhost:8000/health || exit 1
        docker stop test-container

  # Job 5: Dependency Check
  dependency-check:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Check for outdated dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pip-audit
        pip-audit --requirement requirements.txt --format=json --output=audit-report.json || true

    - name: Upload dependency audit report
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: dependency-audit
        path: audit-report.json
