# Bana Chef Server

FastAPI server for Bana Chef cooking application with AI-powered recipe suggestions and image scanning capabilities.

## Features

- 🚀 **FastAPI** - Modern, fast web framework
- 🔐 **JWT Authentication** - Secure user authentication
- 🗄️ **PostgreSQL** - Robust database with SQLAlchemy ORM
- 🤖 **Google Gemini AI** - AI-powered recipe suggestions
- ☁️ **Google Cloud Storage** - File storage for images
- 🐳 **Docker** - Containerized deployment
- 📝 **Auto-generated API docs** - Interactive API documentation

## Project Structure

```
bana_chef_server/
├── app/
│   ├── api/v1/          # API routes
│   ├── core/            # Core configuration
│   ├── crud/            # Database operations
│   ├── db/              # Database setup
│   ├── models/          # SQLAlchemy models
│   ├── schemas/         # Pydantic schemas
│   └── services/        # Business logic
├── tests/               # Test files
├── Dockerfile
├── docker-compose.yml
└── requirements.txt
```

## Quick Start

### Option 1: Docker Compose (Recommended)

1. **Clone and setup**:
   ```bash
   git clone https://github.com/dugno/banachef-server.git
   cd banachef-server
   ```

2. **Create environment file**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start all services**:
   ```bash
   make up
   # or
   docker-compose up -d
   ```

4. **View logs**:
   ```bash
   make logs
   ```

### Option 2: Local Development

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Setup environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Run development server**:
   ```bash
   make dev
   # or
   python run_dev.py
   ```

## API Documentation

Once the server is running, visit:
- **Interactive API docs**: http://localhost:8000/docs
- **Alternative docs**: http://localhost:8000/redoc

## Environment Variables

Copy `.env.example` to `.env` and configure:

```env
# Required
SECRET_KEY=your-super-secret-key
JWT_SECRET_KEY=your-jwt-secret-key
DATABASE_URL=postgresql://user:password@localhost:5432/banachef_db
GOOGLE_API_KEY=your-google-gemini-api-key

# Optional
DEBUG=True
HOST=0.0.0.0
PORT=8000
```

## Available Commands

```bash
make help          # Show all available commands
make install       # Install dependencies
make dev           # Run development server
make build         # Build Docker image
make up            # Start all services
make down          # Stop all services
make logs          # View logs
make clean         # Clean up Docker resources
make test          # Run tests
```

## Development

### Adding New Features

1. **Models**: Add SQLAlchemy models in `app/models/`
2. **Schemas**: Add Pydantic schemas in `app/schemas/`
3. **CRUD**: Add database operations in `app/crud/`
4. **Routes**: Add API endpoints in `app/api/v1/`
5. **Services**: Add business logic in `app/services/`

### Testing

```bash
make test
# or
pytest tests/ -v
```

## Deployment

### Production Docker

```bash
docker build -t banachef-server .
docker run -p 8000:8000 --env-file .env banachef-server
```

### Health Check

```bash
curl http://localhost:8000/health
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.
