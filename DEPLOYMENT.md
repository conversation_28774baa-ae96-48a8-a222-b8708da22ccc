# 🚀 Deployment Guide for Bana Chef Server

This guide covers the complete CI/CD setup and deployment process for the Bana Chef Server.

## 📋 Table of Contents

1. [CI/CD Overview](#cicd-overview)
2. [Prerequisites](#prerequisites)
3. [Environment Setup](#environment-setup)
4. [GitHub Actions Setup](#github-actions-setup)
5. [Production Deployment](#production-deployment)
6. [Monitoring & Maintenance](#monitoring--maintenance)
7. [Troubleshooting](#troubleshooting)

## 🔄 CI/CD Overview

Our CI/CD pipeline consists of:

### Continuous Integration (CI)
- **Code Quality**: Black, isort, flake8, mypy
- **Security Scanning**: Bandit, Safety, pip-audit
- **Testing**: Unit tests, integration tests with PostgreSQL
- **Docker Build**: Multi-stage Docker builds with caching
- **Dependency Auditing**: Automated vulnerability scanning

### Continuous Deployment (CD)
- **Automated Builds**: Docker images pushed to GitHub Container Registry
- **Environment Deployment**: Staging and Production environments
- **Database Migrations**: Automated schema updates
- **Health Checks**: Comprehensive application monitoring
- **Rollback Capability**: Quick rollback to previous versions

## 📋 Prerequisites

### Development Environment
```bash
# Required software
- Python 3.11+
- Docker & Docker Compose
- Git
- Node.js (for some dev tools)

# Install development dependencies
pip install -r requirements-dev.txt

# Setup pre-commit hooks
pre-commit install
```

### Production Server
```bash
# Server requirements
- Ubuntu 20.04+ or CentOS 8+
- Docker & Docker Compose
- Nginx (handled by container)
- SSL certificates (Let's Encrypt recommended)
- Minimum 2GB RAM, 2 CPU cores
- 20GB+ storage
```

## 🔧 Environment Setup

### 1. Environment Variables

Create environment files for each environment:

```bash
# Development
cp .env.example .env

# Production
cp .env.production .env.prod
```

### 2. Required Secrets

Set up the following secrets in your repository and server:

#### GitHub Secrets (Repository Settings > Secrets)
```
STAGING_DATABASE_URL=**************************************/db
PRODUCTION_DATABASE_URL=***********************************/db
DOCKER_REGISTRY_TOKEN=ghp_xxxxxxxxxxxx
STAGING_SERVER_HOST=staging.banachef.com
PRODUCTION_SERVER_HOST=api.banachef.com
STAGING_SSH_KEY=-----BEGIN OPENSSH PRIVATE KEY-----
PRODUCTION_SSH_KEY=-----BEGIN OPENSSH PRIVATE KEY-----
```

#### Production Server Environment
```bash
# Create .env file on production server
sudo nano /opt/banachef/.env

# Essential variables (see .env.production for complete list)
SECRET_KEY=your-super-secure-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here
DATABASE_URL=**********************************/banachef_db
GOOGLE_API_KEY=your-google-api-key
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
```

## 🚀 GitHub Actions Setup

### 1. Workflow Files

The CI/CD pipeline includes three main workflows:

- **`.github/workflows/ci.yml`**: Continuous Integration
- **`.github/workflows/cd.yml`**: Continuous Deployment
- **`.github/workflows/dependency-update.yml`**: Automated dependency updates

### 2. Branch Protection

Set up branch protection rules:

```bash
# Main branch protection
- Require pull request reviews
- Require status checks to pass
- Require branches to be up to date
- Include administrators
```

### 3. Environments

Configure GitHub Environments:

#### Staging Environment
- **Environment name**: `staging`
- **Deployment URL**: `https://staging-api.banachef.com`
- **Required reviewers**: None (auto-deploy)

#### Production Environment
- **Environment name**: `production`
- **Deployment URL**: `https://api.banachef.com`
- **Required reviewers**: 1+ team members
- **Deployment branches**: Only `main` and tags

## 🏭 Production Deployment

### 1. Server Setup

```bash
# 1. Create application directory
sudo mkdir -p /opt/banachef
cd /opt/banachef

# 2. Clone repository
git clone https://github.com/your-username/banachef-server.git .

# 3. Set up environment
cp .env.production .env
# Edit .env with your actual values

# 4. Create required directories
sudo mkdir -p logs backups ssl uploads

# 5. Set permissions
sudo chown -R $USER:$USER /opt/banachef
```

### 2. SSL Certificate Setup

```bash
# Using Let's Encrypt with Certbot
sudo apt install certbot

# Generate certificates
sudo certbot certonly --standalone -d api.banachef.com

# Copy certificates to nginx directory
sudo cp /etc/letsencrypt/live/api.banachef.com/fullchain.pem ssl/
sudo cp /etc/letsencrypt/live/api.banachef.com/privkey.pem ssl/

# Set up auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 3. Initial Deployment

```bash
# 1. Deploy using script
./scripts/deploy.sh deploy production

# 2. Or manually with docker-compose
docker-compose -f docker-compose.prod.yml up -d

# 3. Check deployment
./scripts/deploy.sh health
```

### 4. Database Setup

```bash
# 1. Create initial tables
docker-compose -f docker-compose.prod.yml exec api python create_tables.py

# 2. Run any migrations
docker-compose -f docker-compose.prod.yml exec api python migrate.py
```

## 📊 Monitoring & Maintenance

### 1. Health Monitoring

```bash
# Check application health
curl https://api.banachef.com/health

# Check all services
docker-compose -f docker-compose.prod.yml ps

# View logs
docker-compose -f docker-compose.prod.yml logs -f api
```

### 2. Backup Strategy

```bash
# Automated backups (included in deploy script)
./scripts/deploy.sh backup

# Manual backup
docker-compose -f docker-compose.prod.yml exec db pg_dump \
  -U banachef_admin banachef_db > backup_$(date +%Y%m%d).sql
```

### 3. Log Management

```bash
# Application logs
tail -f /opt/banachef/logs/app.log

# Nginx logs
tail -f /opt/banachef/logs/nginx/access.log
tail -f /opt/banachef/logs/nginx/error.log

# Docker logs
docker-compose -f docker-compose.prod.yml logs --tail=100 -f
```

### 4. Performance Monitoring

Optional monitoring stack (enable with `--profile monitoring`):

```bash
# Start monitoring services
docker-compose -f docker-compose.prod.yml --profile monitoring up -d

# Access Grafana
open http://localhost:3000
# Default: admin / (password from .env)

# Access Prometheus
open http://localhost:9090
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Database Connection Issues
```bash
# Check database status
docker-compose -f docker-compose.prod.yml exec db pg_isready

# Check database logs
docker-compose -f docker-compose.prod.yml logs db

# Reset database connection
docker-compose -f docker-compose.prod.yml restart db api
```

#### 2. SSL Certificate Issues
```bash
# Check certificate validity
openssl x509 -in ssl/fullchain.pem -text -noout

# Renew certificates
sudo certbot renew
./scripts/deploy.sh deploy production
```

#### 3. Memory Issues
```bash
# Check memory usage
docker stats

# Restart services if needed
docker-compose -f docker-compose.prod.yml restart

# Check system resources
free -h
df -h
```

#### 4. Application Errors
```bash
# Check application logs
docker-compose -f docker-compose.prod.yml logs api

# Check health endpoint
curl -v https://api.banachef.com/health

# Restart application
docker-compose -f docker-compose.prod.yml restart api
```

### Emergency Procedures

#### Rollback Deployment
```bash
# Quick rollback
./scripts/deploy.sh rollback

# Manual rollback to specific version
docker-compose -f docker-compose.prod.yml down
docker pull ghcr.io/your-username/banachef-server:previous-tag
# Update docker-compose.yml with previous tag
docker-compose -f docker-compose.prod.yml up -d
```

#### Database Recovery
```bash
# Restore from backup
gunzip -c /opt/banachef/backups/banachef_backup_YYYYMMDD_HHMMSS.sql.gz | \
docker-compose -f docker-compose.prod.yml exec -T db psql -U banachef_admin banachef_db
```

## 📞 Support

For deployment issues:

1. Check the logs first
2. Review this documentation
3. Check GitHub Issues
4. Contact the development team

## 🔄 Continuous Improvement

Regular maintenance tasks:

- **Weekly**: Review security scan results
- **Monthly**: Update dependencies
- **Quarterly**: Review and update deployment procedures
- **Annually**: Security audit and penetration testing
