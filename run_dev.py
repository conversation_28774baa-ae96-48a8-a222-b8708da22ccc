#!/usr/bin/env python3
"""
Development server runner for Bana Chef Server
"""
import uvicorn
from app.core.config import settings

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",  # <PERSON><PERSON><PERSON> bảo host là 0.0.0.0 để có thể truy cập từ xa
        port=settings.PORT,
        reload=True,  # Auto-reload on code changes
        log_level=settings.LOG_LEVEL.lower(),
        access_log=True,
    )
